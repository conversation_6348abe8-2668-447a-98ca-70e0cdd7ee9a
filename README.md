<h1>Dev Documentation Minewache Mod</h1>

<details>

<summary>Economy System</summary>

### E<PERSON><PERSON>hritt:
Du musst eine Verbindung mit der Klasse EcoGlobal.java herstellen. Diese Klasse ist für die Verwaltung des gesamten Wirtschaftssystems verantwortlich.

Das kannst du mit folgendem Code machen:
```java
import net.mcreator.sarosnewblocksmod.EcoGlobal;

static EcoGlobal beispielName = new EcoGlobal();
```
Falls du auf den Fehler stößt, dass du einen Parameter benötigst mache es folgendermaßen:
```java
import net.mcreator.sarosnewblocksmod.EcoGlobal;

public class Klassenname extends ElementsSarosNewBlocksModMod.ModElement {//ersetze das hier nicht in deiner Klasse
    
    static EcoGlobal beispielName;
    
    public Klassenname(ElementsSaorsNewBlocksModMod instance) {
        super(instance, 1); //ersetze das hier nicht in deiner Klasse
        beispielName = new EcoGlobal(instance);
    }
}
```

### Zweiter Schritt:
Du hast nun eine Verbindung zur Klasse EcoGlobal.java hergestellt. Nun kannst du die Methoden der Klasse verwenden.

#### Methoden:


```java
double meineVariable = beispielName.getBalance(player); //playerName ist ein String
```

```java
beispielName.setBalance(player,amount); //player ist ein String und amount ist ein double
```
```java
beispielName.addBalance(player,amount); //player ist ein String und amount ist ein double
```
```java
beispielName.removeBalance(player,amount); //player ist ein String und amount ist ein double
```




Für mehr Informationen schaue in die Klasse EcoGlobal.java.
</details>

<details>

<summary>Farben System</summary>

### Import:
```java
import net.mcreator.sarosnewblocksmod.Dateiverwaltung;
```

## Einfache verwendung
```java
Dateiverwaltung.standard + 
Dateiverwaltung.warning + 
Dateiverwaltung.error + 
Dateiverwaltung.sucess + 
Dateiverwaltung.playername + 
Dateiverwaltung.sendername + 
```

## Normale verwendung

### Strings:

```java
String standardFarbe = Dateiverwaltung.standard;
String warningFarbe = Dateiverwaltung.warning;
String errorFarbe = Dateiverwaltung.error;
String sucessFarbe = Dateiverwaltung.sucess;
String playernameFarbe = Dateiverwaltung.playername;
String sendernameFarbe = Dateiverwaltung.sendername;
```

### Verwendungs Beispiel:

```java
sender.sendMessage(new TextComponentString(warningFarbe + "Der Befehl unterstützt keine Argumente."));
```





</details>

<details>

<summary>Ausweis System</summary>

## Import:
```java
import net.mcreator.sarosnewblocksmod.AusweisGlobal;
```
Ausweis hinzufügen 
```java
AusweisGlobal.addAusweis(uuid, name, lastName, date, height, sex);
```

Ausweis auslesen
```java
AusweisGlobal.getAusweisInfo(getUuid);
```

Ausweis entfernen
```java
AusweisGlobal.removeAusweis(removeUuid);
```


Ganzes beispiel in der class commands/Ausweistest
</details>
