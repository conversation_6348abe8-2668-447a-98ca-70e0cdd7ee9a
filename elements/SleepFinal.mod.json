{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"0\" y=\"0\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"entity_close_gui\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></next></block><block type=\"java_code\" x=\"625\" y=\"192\"><field name=\"CODE\">\t\tEntity entity = (Entity) dependencies.get(\"entity\");</field><next><block type=\"java_code\"><field name=\"CODE\">    if (entity instanceof EntityPlayerMP) {</field><next><block type=\"java_code\"><field name=\"CODE\">        EntityPlayerMP playerMP = (EntityPlayerMP) entity;</field><next><block type=\"java_code\"><field name=\"CODE\">        playerMP.connection.disconnect(new </field><next><block type=\"java_code\"><field name=\"CODE\">TextComponentString(\"Du bist eingeschlafen!\"));</field><next><block type=\"java_code\"><field name=\"CODE\">}</field></block></next></block></next></block></next></block></next></block></next></block></xml>"}}