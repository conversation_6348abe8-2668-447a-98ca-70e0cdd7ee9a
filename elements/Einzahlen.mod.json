{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><variables><variable type=\"String\" id=\"euro\">euro</variable><variable type=\"Number\" id=\"amount\">amount</variable><variable type=\"Number\" id=\"einzahlen\">einza<PERSON>en</variable></variables><block type=\"event_trigger\" deletable=\"false\" x=\"-93\" y=\"82\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"variables_set_number\"><field name=\"VAR\">local:amount</field><value name=\"VAL\"><block type=\"itemstack_get_count\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"variables_set_text\"><field name=\"VAR\">local:euro</field><value name=\"VAL\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"controls_if\"><mutation elseif=\"8\" else=\"1\"></mutation><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro500</field></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">500</field></block></value></block></value></block></statement><value name=\"IF1\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro200</field></block></value></block></value></block></value><statement name=\"DO1\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">200</field></block></value></block></value></block></statement><value name=\"IF2\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro100</field></block></value></block></value></block></value><statement name=\"DO2\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value></block></value></block></statement><value name=\"IF3\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro50</field></block></value></block></value></block></value><statement name=\"DO3\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">50</field></block></value></block></value></block></statement><value name=\"IF4\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro20</field></block></value></block></value></block></value><statement name=\"DO4\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">20</field></block></value></block></value></block></statement><value name=\"IF5\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro10</field></block></value></block></value></block></value><statement name=\"DO5\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">10</field></block></value></block></value></block></statement><value name=\"IF6\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro5</field></block></value></block></value></block></value><statement name=\"DO6\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value></block></value></block></statement><value name=\"IF7\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro2</field></block></value></block></value></block></value><statement name=\"DO7\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value></block></value></block></statement><value name=\"IF8\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"variables_get_text\"><field name=\"VAR\">local:euro</field></block></value><value name=\"B\"><block type=\"item_name\"><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro1</field></block></value></block></value></block></value><statement name=\"DO8\"><block type=\"variables_set_number\"><field name=\"VAR\">local:einzahlen</field><value name=\"VAL\"><block type=\"variables_get_number\"><field name=\"VAR\">local:amount</field></block></value></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text\"><field name=\"TEXT\">�cDu Kannst nur Geld einzahlen!</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement><next><block type=\"controls_if\"><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"math_binary_ops\"><field name=\"OP\">EQ</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:einzahlen</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">�6Du hast �a</field></block></value><value name=\"ADD1\"><block type=\"variables_get_number\"><field name=\"VAR\">local:einzahlen</field></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\">� �6eingezahlt!</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"1\"></mutation><value name=\"ADD0\"><block type=\"variables_get_number\"><field name=\"VAR\">local:einzahlen</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></next></block></next></block></statement></block></next></block></next></block></next></block></next></block></xml>"}}