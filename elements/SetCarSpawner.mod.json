{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><variables><variable type=\"String\" id=\"command\">command</variable></variables><block type=\"event_trigger\" deletable=\"false\" x=\"18\" y=\"-33\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carregistername</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\">carregistername2</field></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carname</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\">carname2</field></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">active</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\">1b</field></block></value><next><block type=\"entity_close_gui\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></next></block></next></block></next></block></next></block></xml>"}}