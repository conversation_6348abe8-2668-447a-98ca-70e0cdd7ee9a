{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><variables><variable type=\"MCItem\" id=\"key\">key</variable><variable type=\"String\" id=\"NBTData\">NBTData</variable><variable type=\"String\" id=\"VehicleName\">VehicleName</variable><variable type=\"String\" id=\"VehicleID\">VehicleID</variable></variables><block type=\"event_trigger\" deletable=\"false\" x=\"0\" y=\"0\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"variables_set_itemstack\"><field name=\"VAR\">local:key</field><value name=\"VAL\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><next><block type=\"variables_set_text\"><field name=\"VAR\">local:VehicleID</field><value name=\"VAL\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleId</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><next><block type=\"variables_set_text\"><field name=\"VAR\">local:VehicleName</field><value name=\"VAL\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleName</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><next><block type=\"controls_if\"><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"item_name\"><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><value name=\"B\"><block type=\"text\"><field name=\"TEXT\">Car keys</field></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VRS</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><value name=\"B\"><block type=\"text\"><field name=\"TEXT\"></field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_execute_command\"><value name=\"command\"><block type=\"text_join\"><mutation items=\"4\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">/summon dynamxmod:entity_car </field></block></value><value name=\"ADD1\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">cords</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD3\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"variables_get_text\"><field name=\"VAR\">local:VehicleID</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">�7Du hast �e</field></block></value><value name=\"ADD1\"><block type=\"text_replace\"><value name=\"what\"><block type=\"text\"><field name=\"TEXT\">vehicle_</field></block></value><value name=\"with\"><block type=\"text\"><field name=\"TEXT\"></field></block></value><value name=\"text\"><block type=\"variables_get_text\"><field name=\"VAR\">local:VehicleName</field></block></value></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\"> �aErfolgreich �7ausgeparkt</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></next></block></statement></block></statement></block></next></block></next></block></next></block></next></block></xml>"}}