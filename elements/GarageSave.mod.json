{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><variables><variable type=\"MCItem\" id=\"key\">key</variable><variable type=\"String\" id=\"UUID\">UUID</variable><variable type=\"String\" id=\"NBTData\">NBTData</variable></variables><block type=\"event_trigger\" deletable=\"false\" x=\"295\" y=\"29\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"variables_set_itemstack\"><field name=\"VAR\">local:key</field><value name=\"VAL\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><next><block type=\"variables_set_text\"><field name=\"VAR\">local:NBTData</field><value name=\"VAL\"><block type=\"text\"><field name=\"TEXT\">NBTData Placeholder</field></block></value><next><block type=\"controls_if\"><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"item_name\"><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><value name=\"B\"><block type=\"text\"><field name=\"TEXT\">Car keys</field></block></value></block></value><statement name=\"DO0\"><block type=\"execute_command\"><value name=\"command\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">kill </field></block></value><value name=\"ADD1\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleId</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleId</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_close_gui\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">getuuidinfo </field></block></value><value name=\"ADD1\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleId</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\"> false</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"variables_get_text\"><field name=\"VAR\">local:NBTData</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleName</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"variables_get_text\"><field name=\"VAR\">local:NBTData</field></block></value></block></next></block></next></block></next></block></next></block></next></block></statement></block></next></block></next></block></next></block></xml>"}}