{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"40\" y=\"40\"><field name=\"trigger\">player_ticks</field><next><block type=\"controls_if\"><value name=\"IF0\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><statement name=\"DO0\"><block type=\"controls_if\"><value name=\"IF0\"><block type=\"logic_binary_ops\"><field name=\"OP\">OR</field><value name=\"A\"><block type=\"compare_mcblocks\"><value name=\"a\"><block type=\"mcitem_allblocks\"><field name=\"value\">CUSTOM:ForSaleSign</field></block></value><value name=\"b\"><block type=\"world_data_blockat\"><value name=\"x\"><block type=\"entity_lookpos_x\"><field name=\"fluid_mode\">NONE</field><field name=\"block_mode\">OUTLINE</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"maxdistance\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value></block></value><value name=\"y\"><block type=\"entity_lookpos_y\"><field name=\"fluid_mode\">NONE</field><field name=\"block_mode\">OUTLINE</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"maxdistance\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value></block></value><value name=\"z\"><block type=\"entity_lookpos_z\"><field name=\"fluid_mode\">NONE</field><field name=\"block_mode\">OUTLINE</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"maxdistance\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value></block></value></block></value></block></value><value name=\"B\"><block type=\"compare_mcblocks\"><value name=\"a\"><block type=\"mcitem_allblocks\"><field name=\"value\">Blocks.AIR</field></block></value><value name=\"b\"><block type=\"world_data_blockat\"><value name=\"x\"><block type=\"entity_lookpos_x\"><field name=\"fluid_mode\">NONE</field><field name=\"block_mode\">OUTLINE</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"maxdistance\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value></block></value><value name=\"y\"><block type=\"entity_lookpos_y\"><field name=\"fluid_mode\">NONE</field><field name=\"block_mode\">OUTLINE</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"maxdistance\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value></block></value><value name=\"z\"><block type=\"entity_lookpos_z\"><field name=\"fluid_mode\">NONE</field><field name=\"block_mode\">OUTLINE</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"maxdistance\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"return_logic\"><value name=\"return\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement></block></statement><next><block type=\"return_logic\"><value name=\"return\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></next></block></next></block></xml>"}}