{"_fv": 18, "_type": "gui", "definition": {"type": 1, "width": 196, "height": 166, "inventoryOffsetX": 0, "inventoryOffsetY": -2, "renderBgLayer": true, "doesPauseGame": false, "components": [{"type": "button", "data": {"text": "<PERSON><PERSON><PERSON><PERSON>", "onClick": {"name": "Payup"}, "width": 75, "height": 20, "name": "<PERSON><PERSON><PERSON><PERSON>", "x": 174, "y": 90}}, {"type": "label", "data": {"text": "Betrag zum Bezahlen:", "color": {"value": -12829636, "falpha": 0.0}, "name": "Betrag zum Bezahlen:", "x": 121, "y": 57}}, {"type": "label", "data": {"text": "Bezahlt:", "color": {"value": -12829636, "falpha": 0.0}, "name": "Bezahlt:", "x": 171, "y": 74}}, {"type": "label", "data": {"text": "<ENBT:number:betrag>�", "color": {"value": -16738048, "falpha": 0.0}, "name": "<ENBT:number:betrag>�", "x": 228, "y": 57}}, {"type": "label", "data": {"text": "<ENBT:logic:bezahlt>", "color": {"value": -12829636, "falpha": 0.0}, "name": "<ENBT:logic:bezahlt>", "x": 218, "y": 74}}]}}