{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"40\" y=\"40\"><field name=\"trigger\">player_right_click_item</field><next><block type=\"controls_if\"><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"entity_issneaking\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation elseif=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:KartenleserItem</field></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">OR</field><value name=\"A\"><block type=\"math_binary_ops\"><field name=\"OP\">EQ</field><value name=\"A\"><block type=\"item_nbt_num_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">betrag</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value></block></value><value name=\"B\"><block type=\"math_binary_ops\"><field name=\"OP\">EQ</field><value name=\"A\"><block type=\"item_nbt_num_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">reset</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"entity_open_gui\"><field name=\"guiname\">KartenGUI2</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><next><block type=\"item_nbt_num_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">reset</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"tagValue\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><next><block type=\"item_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">player</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"tagValue\"><block type=\"entity_name\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></next></block></next></block></statement><value name=\"IF1\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:KartenleserItem</field></block></value></block></value><statement name=\"DO1\"><block type=\"item_nbt_num_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">reset</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"tagValue\"><block type=\"math_dual_ops\"><field name=\"OP\">ADD</field><value name=\"A\"><block type=\"item_nbt_num_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">reset</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">\" + Dateiverwaltung.warning + \"</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Es wurde bereits ein betrag gesetzt dr�cke erneut um ihn zu �berschreiben</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></next></block></statement></block></statement></block></next></block></xml>"}}