{"_fv": 18, "_type": "gui", "definition": {"type": 1, "width": 512, "height": 512, "inventoryOffsetX": 255, "inventoryOffsetY": 255, "renderBgLayer": true, "doesPauseGame": false, "components": [{"type": "button", "data": {"text": "X", "onClick": {"name": "ExitGui"}, "width": 20, "height": 20, "name": "X", "x": 407, "y": 0}}, {"type": "button", "data": {"text": "Einschlafen", "onClick": {"name": "SleepFinal"}, "width": 80, "height": 20, "name": "Einschlafen", "x": 170, "y": 103}}]}}