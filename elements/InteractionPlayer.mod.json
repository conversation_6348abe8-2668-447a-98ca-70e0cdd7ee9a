{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"-71\" y=\"111\"><field name=\"trigger\">player_right_click_entity</field></block><block type=\"entity_open_gui\" x=\"-71\" y=\"227\"><field name=\"guiname\">Givecardinfo</field><value name=\"entity\"><block type=\"source_entity_from_deps\"></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block><block type=\"entity_from_deps\" x=\"601\" y=\"313\"></block><block type=\"controls_if\" x=\"-102\" y=\"381\"><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><next><block type=\"controls_if\"><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"source_entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_open_gui\"><field name=\"guiname\">Givecardinfo</field><value name=\"entity\"><block type=\"source_entity_from_deps\"></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></statement></block></next></block><block type=\"entity_from_deps\" x=\"661\" y=\"410\"></block><block type=\"controls_if\" x=\"-26\" y=\"476\"><value name=\"IF0\"><block type=\"entity_issneaking\"><value name=\"entity\"><block type=\"source_entity_from_deps\"></block></value></block></value></block><block type=\"immediate_source_entity_from_deps\" x=\"593\" y=\"446\"></block><block type=\"compare_mcitems\" x=\"131\" y=\"545\"><value name=\"a\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"source_entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:KartenleserItem</field></block></value></block><block type=\"entity_iterator\" x=\"736\" y=\"525\"></block><block type=\"entity_open_gui\" x=\"126\" y=\"602\"><field name=\"guiname\">KartenGUI</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></xml>"}}