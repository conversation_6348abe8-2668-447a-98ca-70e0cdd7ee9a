{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><variables><variable type=\"String\" id=\"command\">command</variable></variables><block type=\"event_trigger\" deletable=\"false\" x=\"61\" y=\"-129\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">�fFahrzeug </field></block></value><value name=\"ADD1\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carname</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\"> �fwurde �aerfolgreich�f gespawnt</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"execute_command\"><value name=\"command\"><block type=\"text_join\"><mutation items=\"13\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">basicsspawn </field></block></value><value name=\"ADD1\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carregistername</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD3\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">X</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD4\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD5\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">Y</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD6\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD7\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">Z</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD8\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD9\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">rotation</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD10\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD11\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">vra</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD12\"><block type=\"text\"><field name=\"TEXT\"> @p</field></block></value></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><next><block type=\"entity_nbt_text_set\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">canspawn</field></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\">false</field></block></value><next><block type=\"entity_close_gui\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></next></block></next></block></next></block></next></block><block type=\"entity_send_chat\" x=\"31\" y=\"682\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"13\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">basicsspawn </field></block></value><value name=\"ADD1\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carregistername</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD3\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">X</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD4\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD5\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">Y</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD6\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD7\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">Z</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD8\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD9\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">rotation</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD10\"><block type=\"text\"><field name=\"TEXT\"> </field></block></value><value name=\"ADD11\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">vra</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD12\"><block type=\"text\"><field name=\"TEXT\"> @p</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></xml>"}}