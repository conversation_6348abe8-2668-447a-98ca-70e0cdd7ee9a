{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"236\" y=\"308\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\"></field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">name</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\"></field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">worth</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\"></field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">adress</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">�5</field></block></value><value name=\"ADD1\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">name</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"10\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Betrag: </field></block></value><value name=\"ADD2\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.playername</field></block></value><value name=\"ADD3\"><block type=\"math_dual_ops\"><field name=\"OP\">MINUS</field><value name=\"A\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">worth</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">worth</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"B\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">rabatt</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value></block></value></block></value><value name=\"ADD4\"><block type=\"text\"><field name=\"TEXT\">� </field></block></value><value name=\"ADD5\"><block type=\"text\"><field name=\"TEXT\">(</field></block></value><value name=\"ADD6\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">worth</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"ADD7\"><block type=\"text\"><field name=\"TEXT\">�, </field></block></value><value name=\"ADD8\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">rabatt</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"ADD9\"><block type=\"text\"><field name=\"TEXT\">%)</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"4\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Adresse: </field></block></value><value name=\"ADD2\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.playername</field></block></value><value name=\"ADD3\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">adress</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"controls_if\"><value name=\"IF0\"><block type=\"entity_canusecommand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"permissionlevel\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"4\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Region: </field></block></value><value name=\"ADD2\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.playername</field></block></value><value name=\"ADD3\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">region</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"17\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">PinBoard: </field></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\">�6X: </field></block></value><value name=\"ADD3\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.playername</field></block></value><value name=\"ADD4\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">xpin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD5\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD6\"><block type=\"text\"><field name=\"TEXT\"> Y: </field></block></value><value name=\"ADD7\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.playername</field></block></value><value name=\"ADD8\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">ypin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD9\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD10\"><block type=\"text\"><field name=\"TEXT\"> Z: </field></block></value><value name=\"ADD11\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.playername</field></block></value><value name=\"ADD12\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">zpin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"ADD13\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD14\"><block type=\"text\"><field name=\"TEXT\"> Facing: </field></block></value><value name=\"ADD15\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.playername</field></block></value><value name=\"ADD16\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">facing</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text\"><field name=\"TEXT\">�5Items: </field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Item 1: </field></block></value><value name=\"ADD2\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">item1</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Item 2: </field></block></value><value name=\"ADD2\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">item2</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Item 3: </field></block></value><value name=\"ADD2\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">item3</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.standard</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Item 4: </field></block></value><value name=\"ADD2\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">item4</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></next></block></next></block></next></block></next></block></next></block></next></block></statement></block></next></block></next></block></next></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Es sind keine Daten vorhanden.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Es sind keine Daten vorhanden.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Es sind keine Daten vorhanden.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement></block></next></block></xml>"}}