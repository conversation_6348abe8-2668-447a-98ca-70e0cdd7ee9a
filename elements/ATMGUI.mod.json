{"_fv": 18, "_type": "gui", "definition": {"type": 1, "width": 396, "height": 230, "inventoryOffsetX": -110, "inventoryOffsetY": 33, "renderBgLayer": true, "doesPauseGame": false, "components": [{"type": "label", "data": {"text": "<PERSON><PERSON>:", "color": {"value": -12829636, "falpha": 0.0}, "name": "<PERSON><PERSON>:", "x": 21, "y": 11}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 0, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #0", "x": 158, "y": 41}}, {"type": "button", "data": {"text": "Ein<PERSON><PERSON><PERSON>", "onClick": {"name": "Ein<PERSON><PERSON><PERSON>"}, "width": 122, "height": 20, "name": "Ein<PERSON><PERSON><PERSON>", "x": 30, "y": 40}}, {"type": "label", "data": {"text": "Einzahlen:", "color": {"value": -12829636, "falpha": 0.0}, "name": "Einzahlen:", "x": 31, "y": 30}}, {"type": "label", "data": {"text": "Auszahlen:", "color": {"value": -12829636, "falpha": 0.0}, "name": "Auszahlen:", "x": 30, "y": 65}}, {"type": "button", "data": {"text": "Au<PERSON>ahl<PERSON>", "onClick": {"name": "Au<PERSON>ahl<PERSON>"}, "width": 70, "height": 20, "name": "Au<PERSON>ahl<PERSON>", "x": 162, "y": 99}}, {"type": "label", "data": {"text": "<BNBT:number:konto>�", "color": {"value": -16751104, "falpha": 0.0}, "name": "<BNBT:number:konto>�", "x": 104, "y": 11}}, {"type": "outputslot", "data": {"id": 1, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #1", "x": 142, "y": 78}}, {"type": "outputslot", "data": {"id": 2, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #2", "x": 142, "y": 100}}, {"type": "outputslot", "data": {"id": 3, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #3", "x": 142, "y": 122}}, {"type": "button", "data": {"text": "1", "onClick": {"name": "B1E"}, "width": 30, "height": 20, "name": "1", "x": 31, "y": 78}}, {"type": "button", "data": {"text": "10", "onClick": {"name": "B10E"}, "width": 35, "height": 20, "name": "10", "x": 63, "y": 78}}, {"type": "button", "data": {"text": "100", "onClick": {"name": "B100E"}, "width": 40, "height": 20, "name": "100", "x": 100, "y": 78}}, {"type": "button", "data": {"text": "2", "onClick": {"name": "B2E"}, "width": 30, "height": 20, "name": "2", "x": 31, "y": 100}}, {"type": "button", "data": {"text": "20", "onClick": {"name": "B20E"}, "width": 35, "height": 20, "name": "20", "x": 63, "y": 100}}, {"type": "button", "data": {"text": "200", "onClick": {"name": "B200E"}, "width": 40, "height": 20, "name": "200", "x": 100, "y": 100}}, {"type": "button", "data": {"text": "5", "onClick": {"name": "B5E"}, "width": 30, "height": 20, "name": "5", "x": 31, "y": 122}}, {"type": "button", "data": {"text": "50", "onClick": {"name": "B50E"}, "width": 35, "height": 20, "name": "50", "x": 63, "y": 122}}, {"type": "button", "data": {"text": "500", "onClick": {"name": "B500E"}, "width": 40, "height": 20, "name": "500", "x": 100, "y": 122}}, {"type": "label", "data": {"text": "<BNBT:number:auszahlen>�", "color": {"value": -16751104, "falpha": 0.0}, "name": "<BNBT:number:auszahlen>�", "x": 162, "y": 88}}, {"type": "button", "data": {"text": "Clear", "onClick": {"name": "Clearmoney"}, "width": 50, "height": 20, "name": "Clear", "x": 162, "y": 122}}], "onClosed": {"name": "ATMGUIThisGUIIsClosed"}}}