{"_fv": 18, "_type": "gui", "definition": {"type": 1, "width": 392, "height": 211, "inventoryOffsetX": 0, "inventoryOffsetY": 23, "renderBgLayer": true, "doesPauseGame": false, "components": [{"type": "button", "data": {"text": "Set", "onClick": {"name": "SetCarSpawner"}, "width": 91, "height": 20, "name": "Set", "x": 167, "y": 121}}, {"type": "label", "data": {"text": "<PERSON><PERSON> das Item mit den Daten in deiner Main Hand", "color": {"value": -12829636, "falpha": 0.0}, "name": "<PERSON><PERSON> das Item mit den Daten in deiner Main Hand", "x": 95, "y": 59}}, {"type": "label", "data": {"text": "Data1 muss folgendes beinhalten: PackName.vehicle_FahrzeugName", "color": {"value": -12829636, "falpha": 0.0}, "name": "Data1 muss folgendes beinhalten: PackName.vehicle_FahrzeugName", "x": 64, "y": 76}}, {"type": "label", "data": {"text": "Beispiel: Saros-DynamX-Car-Pack-V2.vehicle_t6pol ", "color": {"value": -12829636, "falpha": 0.0}, "name": "Beispiel: Saros-DynamX-Car-Pack-V2.vehicle_t6pol ", "x": 92, "y": 102}}, {"type": "label", "data": {"text": "Data2 Spawner Name", "color": {"value": -12829636, "falpha": 0.0}, "name": "Data2 Spawner Name", "x": 167, "y": 89}}]}}