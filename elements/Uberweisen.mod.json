{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><variables><variable type=\"Number\" id=\"betrag\">betrag</variable></variables><block type=\"event_trigger\" deletable=\"false\" x=\"40\" y=\"40\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"entity_close_gui\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></next></block><block type=\"variables_set_number\" x=\"101\" y=\"251\"><field name=\"VAR\">local:betrag</field><value name=\"VAL\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">betrag</field></block></value></block></value><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">GTE</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">1000</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:betrag</field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"5\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">�6Du Hast �a</field></block></value><value name=\"ADD1\"><block type=\"variables_get_number\"><field name=\"VAR\">local:betrag</field></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\">� �6an �a</field></block></value><value name=\"ADD3\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">nameplayer</field></block></value><value name=\"ADD4\"><block type=\"text\"><field name=\"TEXT\">� �6�berwiesen.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"entity_set_display_name\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"displayname\"><block type=\"text\"><field name=\"TEXT\">FLUGZEUG</field></block></value></block></next></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">�6Du hast nicht genug geld Du brauchst noch �c</field></block></value><value name=\"ADD1\"><block type=\"math_dual_ops\"><field name=\"OP\">MINUS</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:betrag</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">1000</field></block></value></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\">�</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></statement></block></next></block></xml>"}}