{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"-134\" y=\"-44\"><field name=\"trigger\">no_ext_trigger</field></block><block type=\"controls_if\" x=\"-137\" y=\"108\"><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"item_name\"><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><value name=\"B\"><block type=\"text\"><field name=\"TEXT\">Car keys</field></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><value name=\"IF0\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleName</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><value name=\"B\"><block type=\"entity_nbt_text_get\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carname</field></block></value></block></value></block></value><value name=\"B\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VR</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><value name=\"B\"><block type=\"text\"><field name=\"TEXT\"></field></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"item_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VR</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"tagValue\"><block type=\"entity_name\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><next><block type=\"item_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VRS</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"tagValue\"><block type=\"entity_nbt_text_get\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carregistername</field></block></value></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">�aFahrzeug Registriert: </field></block></value><value name=\"ADD1\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleName</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"entity_nbt_text_set\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">namespawner</field></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\"></field></block></value><next><block type=\"entity_nbt_text_set\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carname</field></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\">none</field></block></value></block></next></block></next></block></next></block></next></block></statement></block></statement></block></xml>"}}