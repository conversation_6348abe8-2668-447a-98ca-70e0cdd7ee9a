{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><variables><variable type=\"MCItem\" id=\"key\">key</variable></variables><block type=\"event_trigger\" deletable=\"false\" x=\"40\" y=\"40\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"variables_set_itemstack\"><field name=\"VAR\">local:key</field><value name=\"VAL\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"item_name\"><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><value name=\"B\"><block type=\"text\"><field name=\"TEXT\">Car keys</field></block></value></block></value><statement name=\"DO0\"><block type=\"gui_clear_slot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><next><block type=\"execute_command\"><value name=\"command\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">kill </field></block></value><value name=\"ADD1\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VehicleId</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><next><block type=\"entity_nbt_logic_set\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">canspawn</field></block></value><value name=\"tagValue\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"entity_close_gui\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></next></block></next></block></next></block></statement><statement name=\"ELSE\"><block type=\"controls_if\"><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:VRSRemover</field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_nbt_logic_set\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">canspawn</field></block></value><value name=\"tagValue\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"gui_clear_slot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><next><block type=\"entity_close_gui\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></next></block></next></block></statement></block></statement></block></next></block></next></block><block type=\"controls_if\" x=\"169\" y=\"637\"><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"item_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">VRS</field></block></value><value name=\"item\"><block type=\"variables_get_itemstack\"><field name=\"VAR\">local:key</field></block></value></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">carregistername</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></xml>"}}