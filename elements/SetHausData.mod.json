{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"-536\" y=\"658\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">name</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">worth</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">rabatt</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">adress</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">3</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">region</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">4</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">xpin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">ypin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">6</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">zpin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">7</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">facing</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">8</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">item1</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">9</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">item2</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">10</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">item3</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">11</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">item4</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">12</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">xsign</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text_join\"><mutation items=\"1\"></mutation><value name=\"ADD0\"><block type=\"coord_x\"></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">ysign</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text_join\"><mutation items=\"1\"></mutation><value name=\"ADD0\"><block type=\"coord_y\"></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">zsign</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text_join\"><mutation items=\"1\"></mutation><value name=\"ADD0\"><block type=\"coord_z\"></block></value></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">facingsign</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text_join\"><mutation items=\"1\"></mutation><value name=\"ADD0\"><block type=\"world_data_block_direction_at\"><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\">Air</field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">name</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\">Air</field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">worth</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\">Air</field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">adress</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\">Air</field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">region</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\">Air</field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">xpin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\">Air</field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">ypin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\">Air</field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">zpin</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"text\"><field name=\"TEXT\">Air</field></block></value><value name=\"B\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">facing</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">GTE</field><value name=\"A\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">LTE</field><value name=\"A\"><block type=\"text_length\"><value name=\"VALUE\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">9</field></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">LTE</field><value name=\"A\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">GTE</field><value name=\"A\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"item_name\"><value name=\"item\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value></block></value></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.sucess</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Infos wurden gespeichert.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Gebe im Rabatt eine Zahl zwichen 0 und 100 ein.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Gebe im Rabatt eine Zahl zwichen 0 und 100 ein.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Gebe im Betrag eine Zahl zwichen 0 und 999999999.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Gebe im Betrag eine Zahl zwichen 0 und 999999999.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst alle felder angeben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst alle felder angeben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst alle felder angeben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst alle felder angeben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst alle felder angeben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst alle felder angeben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst alle felder angeben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">Dateiverwaltung.warning</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst alle felder angeben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value><next><block type=\"call_procedure_at\"><field name=\"procedure\">ResetInfoHouse</field><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></next></block></statement></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></xml>"}}