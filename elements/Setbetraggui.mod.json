{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"40\" y=\"40\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_binary_ops\" collapsed=\"true\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">a</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">b</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">c</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">d</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">e</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">f</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" inline=\"false\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">g</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">h</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">i</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">j</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">k</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">l</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">m</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">n</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">o</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">p</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">q</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">r</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">s</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">t</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">u</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">v</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">w</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">x</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">y</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\" collapsed=\"true\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">z</field></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\" collapsed=\"true\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">A</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">B</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">C</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">D</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">E</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">F</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">G</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">H</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">I</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">J</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">K</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">L</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">M</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">N</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">O</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">P</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">Q</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">R</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">S</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">T</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">U</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">V</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">W</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">X</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">Y</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">Z</field></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\" collapsed=\"true\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">,</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">'</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">#</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">*</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">~</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">?</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">�</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">}</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">[</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">]</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">{</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">[</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">=</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">)</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">(</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">/</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">&amp;</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">%</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">$</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">�</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">\"</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">!</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">�</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">�</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">�</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">.</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">;</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">:</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">_</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">&lt;</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">&gt;</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">|</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">^</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">�</field></block></value></block></value></block></value><value name=\"B\"><block type=\"logic_negate\"><value name=\"BOOL\"><block type=\"text_contains\"><value name=\"text\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value><value name=\"contains\"><block type=\"text\"><field name=\"TEXT\">`</field></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text\"><field name=\"TEXT\">�aBetrag wurde gesetzt</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value><next><block type=\"item_nbt_num_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">betrag</field></block></value><value name=\"item\"><block type=\"entity_iteminhand\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"tagValue\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value></block></value><next><block type=\"entity_nbt_num_set\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">betrag</field></block></value><value name=\"tagValue\"><block type=\"math_from_text\"><value name=\"NUMTEXT\"><block type=\"gui_get_text_textfield\"><field name=\"textfield\">setbetrag</field></block></value></block></value></block></next></block></next></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">\" + Dateiverwaltung.warning + \"</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">You can only use numbers</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></statement></block></next></block></xml>"}}