{"_fv": 18, "_type": "gui", "definition": {"type": 1, "width": 176, "height": 166, "inventoryOffsetX": 0, "inventoryOffsetY": 0, "renderBgLayer": true, "doesPauseGame": false, "components": [{"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 0, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #0", "x": 132, "y": 41}}, {"type": "button", "data": {"text": "read card", "onClick": {"name": "Readingcards"}, "width": 49, "height": 20, "name": "read card", "x": 130, "y": 63}}]}}