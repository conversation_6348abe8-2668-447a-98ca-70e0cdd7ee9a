{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><variables><variable type=\"Number\" id=\"auszahlen100\">auszahlen100</variable><variable type=\"Number\" id=\"auszahlen10\">auszahlen10</variable><variable type=\"Number\" id=\"over100\">over100</variable><variable type=\"Number\" id=\"zahl_5x\">zahl_5x</variable><variable type=\"Number\" id=\"zahl_2x\">zahl_2x</variable><variable type=\"Number\" id=\"zahl_1x\">zahl_1x</variable><variable type=\"Number\" id=\"zahl_100x\">zahl_100x</variable><variable type=\"Number\" id=\"zahl_10x\">zahl_10x</variable><variable type=\"Number\" id=\"eh\">eh</variable><variable type=\"Number\" id=\"ze\">ze</variable><variable type=\"Number\" id=\"ei\">ei</variable><variable type=\"Number\" id=\"auszahlen\">auszahlen</variable></variables><block type=\"event_trigger\" deletable=\"false\" x=\"-4012\" y=\"-696\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen</field><value name=\"VAL\"><block type=\"block_nbt_num_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">auszahlen</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:zahl_5x</field><value name=\"VAL\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:zahl_2x</field><value name=\"VAL\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:zahl_1x</field><value name=\"VAL\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:zahl_100x</field><value name=\"VAL\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:zahl_10x</field><value name=\"VAL\"><block type=\"math_number\"><field name=\"NUM\">10</field></block></value><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">GTE</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">5000</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">GTE</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">5000</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_binary_ops\" collapsed=\"true\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><value name=\"B\"><block type=\"logic_binary_ops\"><field name=\"OP\">AND</field><value name=\"A\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><value name=\"B\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">3</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation elseif=\"2\" else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">500</field></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro500</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen100</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">500</field></block></value></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:over100</field><value name=\"VAL\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:eh</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value></block></next></block></next></block></next></block></statement><value name=\"IF1\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">200</field></block></value></block></value></block></value><statement name=\"DO1\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro200</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen100</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">200</field></block></value></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:over100</field><value name=\"VAL\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:eh</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value></block></next></block></next></block></next></block></statement><value name=\"IF2\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value></block></value></block></value><statement name=\"DO2\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro100</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen100</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">100</field></block></value></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:over100</field><value name=\"VAL\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:eh</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_100x</field></block></value></block></value></block></next></block></next></block></next></block></statement><statement name=\"ELSE\"><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen100</field><value name=\"VAL\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value></block></statement><next><block type=\"controls_if\"><mutation elseif=\"3\" else=\"1\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">10</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">50</field></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO0\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro50</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro50</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><next><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen10</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">50</field></block></value></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:ze</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value></block></next></block></next></block></statement><value name=\"IF1\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">10</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">20</field></block></value></block></value></block></value><statement name=\"DO1\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO0\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro20</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro20</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><next><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen10</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">20</field></block></value></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:ze</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value></block></next></block></next></block></statement><value name=\"IF2\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">10</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">10</field></block></value></block></value></block></value><statement name=\"DO2\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO0\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro10</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro10</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><next><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen10</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><next><block type=\"variables_set_number\"><field name=\"VAR\">local:ze</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_10x</field></block></value></block></value></block></next></block></next></block></statement><value name=\"IF3\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen100</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value></block></value><statement name=\"DO3\"><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen10</field><value name=\"VAL\"><block type=\"variables_get_number\"><field name=\"VAR\">local:over100</field></block></value></block></statement><statement name=\"ELSE\"><block type=\"variables_set_number\"><field name=\"VAR\">local:auszahlen10</field><value name=\"VAL\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen</field></block></value></block></statement><next><block type=\"controls_if\"><mutation elseif=\"2\"></mutation><value name=\"IF0\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">5</field></block></value></block></value></block></value><statement name=\"DO0\"><block type=\"controls_if\"><mutation elseif=\"1\" else=\"1\"></mutation><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO0\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro5</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><value name=\"IF1\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO1\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro5</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro5</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">3</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><next><block type=\"variables_set_number\"><field name=\"VAR\">local:ei</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_5x</field></block></value></block></value></block></next></block></statement><value name=\"IF1\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value></block></value></block></value><statement name=\"DO1\"><block type=\"controls_if\"><mutation elseif=\"1\" else=\"1\"></mutation><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO0\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro2</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><value name=\"IF1\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO1\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro2</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro2</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">3</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><next><block type=\"variables_set_number\"><field name=\"VAR\">local:ei</field><value name=\"VAL\"><block type=\"math_dual_ops\"><field name=\"OP\">MULTIPLY</field><value name=\"A\"><block type=\"math_dual_ops\"><field name=\"OP\">DIVIDE</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:zahl_2x</field></block></value></block></value></block></next></block></statement><value name=\"IF2\"><block type=\"math_binary_ops\"><field name=\"OP\">GT</field><value name=\"A\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">MOD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"B\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value></block></value></block></value><statement name=\"DO2\"><block type=\"controls_if\"><mutation elseif=\"1\" else=\"1\"></mutation><value name=\"IF0\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO0\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro1</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">1</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><value name=\"IF1\"><block type=\"compare_mcitems\"><value name=\"a\"><block type=\"gui_get_item_inslot\"><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></value><value name=\"b\"><block type=\"mcitem_all\"><field name=\"value\">Blocks.AIR</field></block></value></block></value><statement name=\"DO1\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro1</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">2</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"gui_set_items\"><value name=\"amount\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value><value name=\"item\"><block type=\"mcitem_all\"><field name=\"value\">CUSTOM:Euro1</field></block></value><value name=\"slotid\"><block type=\"math_number\"><field name=\"NUM\">3</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></statement><next><block type=\"variables_set_number\"><field name=\"VAR\">local:ei</field><value name=\"VAL\"><block type=\"variables_get_number\"><field name=\"VAR\">local:auszahlen10</field></block></value></block></next></block></statement><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"3\"></mutation><value name=\"ADD0\"><block type=\"text\"><field name=\"TEXT\">�6Du Hast �a</field></block></value><value name=\"ADD1\"><block type=\"math_dual_ops\"><field name=\"OP\">ADD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:eh</field></block></value><value name=\"B\"><block type=\"math_dual_ops\"><field name=\"OP\">ADD</field><value name=\"A\"><block type=\"variables_get_number\"><field name=\"VAR\">local:ze</field></block></value><value name=\"B\"><block type=\"variables_get_number\"><field name=\"VAR\">local:ei</field></block></value></block></value></block></value><value name=\"ADD2\"><block type=\"text\"><field name=\"TEXT\">� �6Abgeboben.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></next></block></next></block></next></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text\"><field name=\"TEXT\">�cLeere den Automaten um ihn zu benutzen!</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text\"><field name=\"TEXT\">�cDu Hast zu wenig geld!</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text\"><field name=\"TEXT\">�cDu kannst maximal �35000� �cabheben!</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></statement><next><block type=\"block_nbt_num_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">auszahlen</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"math_number\"><field name=\"NUM\">0</field></block></value><next><block type=\"entity_close_gui\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></xml>"}}