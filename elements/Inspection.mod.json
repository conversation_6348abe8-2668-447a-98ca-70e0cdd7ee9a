{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"40\" y=\"40\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"variables_get_logic\"><field name=\"VAR\">global:INSPECT</field></block></value><statement name=\"DO0\"><block type=\"variables_set_logic\"><field name=\"VAR\">global:INSPECT</field></block></statement><statement name=\"ELSE\"><block type=\"variables_set_logic\"><field name=\"VAR\">global:INSPECT</field></block></statement></block></next></block></xml>"}}