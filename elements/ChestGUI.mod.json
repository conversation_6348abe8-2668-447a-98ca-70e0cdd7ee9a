{"_fv": 18, "_type": "gui", "definition": {"type": 1, "width": 176, "height": 166, "inventoryOffsetX": 0, "inventoryOffsetY": 0, "renderBgLayer": true, "doesPauseGame": false, "components": [{"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 0, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #0", "x": 133, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 1, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #1", "x": 151, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 2, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #2", "x": 169, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 3, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #3", "x": 187, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 4, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #4", "x": 205, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 5, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #5", "x": 223, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 6, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #6", "x": 241, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 7, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #7", "x": 259, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 8, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #8", "x": 277, "y": 100}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 9, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #9", "x": 133, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 10, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #10", "x": 151, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 11, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #11", "x": 169, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 12, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #12", "x": 187, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 13, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #13", "x": 205, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 14, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #14", "x": 223, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 15, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #15", "x": 241, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 16, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #16", "x": 259, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 17, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #17", "x": 277, "y": 82}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 18, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #18", "x": 133, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 19, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #19", "x": 151, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 20, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #20", "x": 169, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 21, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #21", "x": 187, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 22, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #22", "x": 205, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 23, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #23", "x": 223, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 24, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #24", "x": 241, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 25, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #25", "x": 259, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 26, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #26", "x": 277, "y": 64}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 27, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #27", "x": 133, "y": 46}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 28, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #28", "x": 151, "y": 46}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 29, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #29", "x": 169, "y": 46}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 30, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #30", "x": 187, "y": 46}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 31, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #31", "x": 205, "y": 46}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 32, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #32", "x": 223, "y": 46}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 33, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #33", "x": 241, "y": 46}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 34, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #34", "x": 259, "y": 46}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 35, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #35", "x": 277, "y": 46}}]}}