{"_fv": 18, "_type": "item", "definition": {"renderType": 1, "texture": "cauldron_inner", "customModelName": "menottes:default", "name": "Handschellen", "rarity": "COMMON", "creativeTab": {"value": "CUSTOM:SarosNewBlocksMod"}, "stackSize": 1, "enchantability": 0, "useDuration": 60, "toolType": 1.0, "damageCount": 0, "recipeRemainder": {"value": ""}, "destroyAnyBlock": false, "immuneToFire": false, "stayInGridWhenCrafting": false, "damageOnCrafting": false, "enableMeleeDamage": false, "damageVsEntity": 0.0, "specialInfo": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "hasGlow": false, "guiBoundTo": "<NONE>", "inventorySize": 9, "inventoryStackSize": 64, "onRightClickedInAir": {"name": "<PERSON><PERSON>"}, "onRightClickedOnBlock": {"name": "<PERSON><PERSON>"}}}