{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"135\" y=\"-93\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"entity_nbt_text_get\"><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">canspawn</field></block></value></block></value><value name=\"B\"><block type=\"text\"><field name=\"TEXT\">false</field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_open_gui\"><field name=\"guiname\">CarDespawnerGUI</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"text_binary_ops\"><value name=\"A\"><block type=\"block_nbt_text_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">active</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"B\"><block type=\"text\"><field name=\"TEXT\">1b</field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_open_gui\"><field name=\"guiname\">CarSpawnerGUI</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">\"Dateiverwaltung.warning + \"</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Der Block wurde noch nicht eingestellt nutze /setcarspawner</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></statement></block></statement></block></next></block></xml>"}}