{"_fv": 18, "_type": "gui", "definition": {"type": 1, "width": 380, "height": 235, "inventoryOffsetX": 61, "inventoryOffsetY": 32, "renderBgLayer": true, "doesPauseGame": false, "components": [{"type": "label", "data": {"text": "Betrag:", "color": {"value": -16777165, "falpha": 0.0}, "name": "Betrag:", "x": 60, "y": 44}}, {"type": "label", "data": {"text": "<PERSON><PERSON><PERSON>:", "color": {"value": -16777165, "falpha": 0.0}, "name": "<PERSON><PERSON><PERSON>:", "x": 54, "y": 80}}, {"type": "button", "data": {"text": "Set", "onClick": {"name": "SetHausData"}, "width": 40, "height": 20, "name": "Set", "x": 357, "y": 206}}, {"type": "label", "data": {"text": "Name:", "color": {"value": -16777165, "falpha": 0.0}, "name": "Name:", "x": 69, "y": 26}}, {"type": "label", "data": {"text": "Region:", "color": {"value": -16777165, "falpha": 0.0}, "name": "Region:", "x": 60, "y": 98}}, {"type": "label", "data": {"text": "Ra<PERSON>t(in %):", "color": {"value": -16777165, "falpha": 0.0}, "name": "Ra<PERSON>t(in %):", "x": 30, "y": 62}}, {"type": "label", "data": {"text": "PinBoard:", "color": {"value": -16777165, "falpha": 0.0}, "name": "PinBoard:", "x": 267, "y": 8}}, {"type": "label", "data": {"text": "X:", "color": {"value": -16777165, "falpha": 0.0}, "name": "X:", "x": 258, "y": 26}}, {"type": "label", "data": {"text": "Y:", "color": {"value": -16777165, "falpha": 0.0}, "name": "Y:", "x": 258, "y": 44}}, {"type": "label", "data": {"text": "Z:", "color": {"value": -16777165, "falpha": 0.0}, "name": "Z:", "x": 258, "y": 62}}, {"type": "label", "data": {"text": "Facing:", "color": {"value": -16777165, "falpha": 0.0}, "name": "Facing:", "x": 231, "y": 80}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 0, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #0", "x": 96, "y": 26}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 1, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #1", "x": 96, "y": 44}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 2, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #2", "x": 96, "y": 62}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 3, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #3", "x": 96, "y": 80}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 4, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #4", "x": 96, "y": 98}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 5, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #5", "x": 267, "y": 26}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 6, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #6", "x": 267, "y": 44}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 7, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #7", "x": 267, "y": 62}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 8, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #8", "x": 267, "y": 80}}, {"type": "label", "data": {"text": "Items:", "color": {"value": -12829636, "falpha": 0.0}, "name": "Items:", "x": 99, "y": 123}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 9, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #9", "x": 78, "y": 134}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 10, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #10", "x": 96, "y": 134}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 11, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #11", "x": 114, "y": 134}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 12, "disableStackInteraction": false, "dropItemsWhenNotBound": true, "name": "Slot #12", "x": 132, "y": 134}}]}}