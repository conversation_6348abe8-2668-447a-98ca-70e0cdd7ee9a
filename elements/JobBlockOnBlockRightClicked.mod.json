{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"40\" y=\"-114\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"java_code\"><field name=\"CODE\">String standardFarbe = Dateiverwaltung.standard;</field><next><block type=\"java_code\"><field name=\"CODE\">String warningFarbe = Dateiverwaltung.warning;</field><next><block type=\"java_code\"><field name=\"CODE\">String errorFarbe = Dateiverwaltung.error;</field><next><block type=\"java_code\"><field name=\"CODE\">String sucessFarbe = Dateiverwaltung.sucess;</field><next><block type=\"java_code\"><field name=\"CODE\">String playernameFarbe = Dateiverwaltung.playername;</field><next><block type=\"java_code\"><field name=\"CODE\">String sendernameFarbe = Dateiverwaltung.sendername;</field><next><block type=\"controls_if\"><mutation else=\"1\"></mutation><value name=\"IF0\"><block type=\"logic_binary_ops\"><field name=\"OP\">EQ</field><value name=\"A\"><block type=\"block_nbt_logic_get\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">active</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></value><value name=\"B\"><block type=\"logic_boolean\"><field name=\"BOOL\">TRUE</field></block></value></block></value><statement name=\"DO0\"><block type=\"entity_open_gui\"><field name=\"guiname\">JobGUI</field><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value></block></statement><statement name=\"ELSE\"><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text_join\"><mutation items=\"2\"></mutation><value name=\"ADD0\"><block type=\"java_code_get\"><field name=\"CODE\">\"\") + warningFarbe + (\"\"</field></block></value><value name=\"ADD1\"><block type=\"text\"><field name=\"TEXT\">Du musst den Block erst einstellen. Nutze /job setblock und schau auf den Block.</field></block></value></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></statement></block></next></block></next></block></next></block></next></block></next></block></next></block></next></block></xml>"}}