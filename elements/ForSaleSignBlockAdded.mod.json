{"_fv": 18, "_type": "procedure", "definition": {"procedurexml": "<xml xmlns=\"https://developers.google.com/blockly/xml\"><block type=\"event_trigger\" deletable=\"false\" x=\"40\" y=\"40\"><field name=\"trigger\">no_ext_trigger</field><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">mx</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\">BlockXCord</field></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">mz</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\">BlockYCord</field></block></value><next><block type=\"block_nbt_text_set\"><value name=\"tagName\"><block type=\"text\"><field name=\"TEXT\">my</field></block></value><value name=\"x\"><block type=\"coord_x\"></block></value><value name=\"y\"><block type=\"coord_y\"></block></value><value name=\"z\"><block type=\"coord_z\"></block></value><value name=\"tagValue\"><block type=\"text\"><field name=\"TEXT\">BlockZCord</field></block></value><next><block type=\"entity_send_chat\"><value name=\"text\"><block type=\"text\"><field name=\"TEXT\">test</field></block></value><value name=\"entity\"><block type=\"entity_from_deps\"></block></value><value name=\"actbar\"><block type=\"logic_boolean\"><field name=\"BOOL\">FALSE</field></block></value></block></next></block></next></block></next></block></next></block></xml>"}}