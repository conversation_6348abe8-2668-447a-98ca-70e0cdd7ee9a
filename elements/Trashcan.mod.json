{"_fv": 18, "_type": "gui", "definition": {"type": 1, "width": 190, "height": 195, "inventoryOffsetX": 0, "inventoryOffsetY": 16, "renderBgLayer": true, "doesPauseGame": false, "components": [{"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 0, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #0", "x": 177, "y": 35}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 1, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #1", "x": 195, "y": 35}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 2, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #2", "x": 213, "y": 35}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 3, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #3", "x": 231, "y": 35}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 4, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #4", "x": 177, "y": 53}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 5, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #5", "x": 195, "y": 53}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 6, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #6", "x": 213, "y": 53}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 7, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #7", "x": 231, "y": 53}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 8, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #8", "x": 177, "y": 71}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 9, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #9", "x": 195, "y": 71}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 10, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #10", "x": 213, "y": 71}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 11, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #11", "x": 231, "y": 71}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 12, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #12", "x": 177, "y": 89}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 13, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #13", "x": 195, "y": 89}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 14, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #14", "x": 213, "y": 89}}, {"type": "inputslot", "data": {"inputLimit": {"value": ""}, "id": 15, "disableStackInteraction": false, "dropItemsWhenNotBound": false, "name": "Slot #15", "x": 231, "y": 89}}, {"type": "image", "data": {"image": "guitrash.png", "use1Xscale": false, "name": "guitrash.png", "x": 149, "y": 5}}]}}