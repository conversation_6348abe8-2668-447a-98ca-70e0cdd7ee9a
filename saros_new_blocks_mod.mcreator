{"id_map": {"ENTITY": 2, "GUI": 68}, "mod_elements": [{"name": "DieMinewache", "type": "item", "sortid": 62, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "die_minewache", "path": "~/CreativeTabs"}, {"name": "SarosNewBlocksMod", "type": "tab", "sortid": 63, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "saros_new_blocks_mod", "path": "~/CreativeTabs"}, {"name": "FullGrassBlock", "type": "block", "sortid": 1, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "full_grass_block", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "GrassStonePath", "type": "block", "sortid": 2, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "grass_stone_path", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "GrassStonePath2", "type": "block", "sortid": 3, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "grass_stone_path_2", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "FullPathBlock", "type": "block", "sortid": 4, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "full_path_block", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "Sidewalk", "type": "block", "sortid": 5, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "sidewalk", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "<PERSON><PERSON><PERSON>", "type": "block", "sortid": 6, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "sidewalkred", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "MinewacheLogo", "type": "block", "sortid": 37, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "minewache_logo", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "Radio", "type": "item", "sortid": 51, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "radio", "path": "~/newblocks"}, {"name": "KlappeRightClickedInAir", "type": "procedure", "sortid": 64, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "klappe_right_clicked_in_air", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/newblocks"}, {"name": "<PERSON><PERSON><PERSON>", "type": "item", "sortid": 50, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "klappe", "path": "~/newblocks"}, {"name": "BehandeltesHolz", "type": "block", "sortid": 10, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "behandel<PERSON>_holz", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "BehandeltesHolzStufe", "type": "block", "sortid": 11, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "behandel<PERSON>_holz_stufe", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "BehandeltesHolzTreppe", "type": "block", "sortid": 12, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "behandel<PERSON>_holz_treppe", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "Samsung", "type": "item", "sortid": 39, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "samsung", "path": "~/newblocks"}, {"name": "Iphone", "type": "item", "sortid": 40, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "iphone", "path": "~/newblocks"}, {"name": "CrimsonTrapdoor", "type": "block", "sortid": 25, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "crimson_trapdoor", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "AcaciaTrapdoor", "type": "block", "sortid": 26, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "acacia_trapdoor", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "WarpedTrapdoor", "type": "block", "sortid": 27, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "warped_trapdoor", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "BirchTrapdoor", "type": "block", "sortid": 28, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "birch_trapdoor", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "NewGravel", "type": "block", "sortid": 7, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "new_gravel", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "NewSand", "type": "block", "sortid": 8, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "new_sand", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "DarkOakTrapdoor", "type": "block", "sortid": 29, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "dark_oak_trapdoor", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "SpruceTrapdoor", "type": "block", "sortid": 30, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "spruce_trapdoor", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "SmoothStone", "type": "block", "sortid": 9, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "smooth_stone", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "JungleTrapdoor", "type": "block", "sortid": 31, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "jungle_trapdoor", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "CoffeeCup", "type": "item", "sortid": 32, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "coffee_cup", "path": "~/deliciousdelights"}, {"name": "Coffee", "type": "food", "sortid": 33, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "coffee", "path": "~/deliciousdelights"}, {"name": "ChocolateDonut", "type": "food", "sortid": 34, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "chocolate_donut", "path": "~/deliciousdelights"}, {"name": "Donut", "type": "food", "sortid": 35, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "donut", "path": "~/deliciousdelights"}, {"name": "Tanksaule", "type": "block", "sortid": 13, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "<PERSON><PERSON><PERSON>", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "block", "sortid": 15, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "kaffee_maschiene", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "Handschellen", "type": "item", "sortid": 14, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "<PERSON><PERSON>len", "path": "~/newblocks"}, {"name": "Telefon", "type": "block", "sortid": 16, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "telefon", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "block", "sortid": 17, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "<PERSON><PERSON><PERSON><PERSON>", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "<PERSON><PERSON>", "type": "block", "sortid": 18, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "kasse", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "Card1", "type": "item", "sortid": 41, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "card_1", "path": "~/Economy-System/Money"}, {"name": "Card2", "type": "item", "sortid": 42, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "card_2", "path": "~/Economy-System/Money"}, {"name": "Card3", "type": "item", "sortid": 43, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "card_3", "path": "~/Economy-System/Money"}, {"name": "Card4", "type": "item", "sortid": 44, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "card_4", "path": "~/Economy-System/Money"}, {"name": "Card5", "type": "item", "sortid": 45, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "card_5", "path": "~/Economy-System/Money"}, {"name": "Card6", "type": "item", "sortid": 46, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "card_6", "path": "~/Economy-System/Money"}, {"name": "Search", "type": "code", "sortid": 65, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "search", "path": "~/RP-<PERSON>uff"}, {"name": "<PERSON><PERSON>", "type": "procedure", "sortid": 66, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "cuff", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/newblocks"}, {"name": "Ausweis", "type": "item", "sortid": 47, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "ausweis", "path": "~/ID-System"}, {"name": "AdminGUI", "type": "gui", "sortid": 67, "compiles": true, "locked_code": false, "ids": {"0": 7}, "registry_name": "admin_gui", "path": "~/Haus-System"}, {"name": "AdminGUIOpen", "type": "code", "sortid": 68, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "admin_gui_open", "path": "~/Haus-System"}, {"name": "Dateiverwaltung", "type": "code", "sortid": 69, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "dateiverwaltung", "path": "~/Dateiverwaltung"}, {"name": "PlayerGUI", "type": "gui", "sortid": 70, "compiles": true, "locked_code": false, "ids": {"0": 9}, "registry_name": "player_gui", "path": "~/Haus-System"}, {"name": "ForSaleSign", "type": "block", "sortid": 20, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "for_sale_sign", "path": "~/Haus-System"}, {"name": "MySQLCreate", "type": "code", "sortid": 71, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "my_sql_create", "path": "~/Dateiverwaltung"}, {"name": "Interact", "type": "keybind", "sortid": 72, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "interact", "path": "~/Haus-System"}, {"name": "ReadDataCommand", "type": "code", "sortid": 73, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "read_data_command", "path": "~/Dateiverwaltung"}, {"name": "ResetInfoHouse", "type": "procedure", "sortid": 74, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "reset_info_house", "metadata": {"dependencies": [{"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Haus-System"}, {"name": "EcoJoin", "type": "code", "sortid": 75, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "eco_join", "path": "~/Economy-System"}, {"name": "EcoCommand", "type": "code", "sortid": 76, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "eco_command", "path": "~/Economy-System"}, {"name": "PinBoard", "type": "block", "sortid": 21, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "pin_board", "path": "~/Haus-System"}, {"name": "Hausinfo", "type": "procedure", "sortid": 77, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "hausinfo", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Haus-System"}, {"name": "ATMGUI", "type": "gui", "sortid": 78, "compiles": true, "locked_code": false, "ids": {"0": 10}, "registry_name": "atmgui", "path": "~/Economy-System"}, {"name": "ATM", "type": "block", "sortid": 19, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "atm", "path": "~/newblocks"}, {"name": "Money", "type": "command", "sortid": 79, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "money", "path": "~/Economy-System"}, {"name": "Euro1", "type": "item", "sortid": 52, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_1", "path": "~/Economy-System/Money"}, {"name": "MinewacheModMoney", "type": "tab", "sortid": 80, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "minewache_mod_money", "path": "~/Economy-System/Money"}, {"name": "Euro2", "type": "item", "sortid": 53, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_2", "path": "~/Economy-System/Money"}, {"name": "Euro5", "type": "item", "sortid": 54, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_5", "path": "~/Economy-System/Money"}, {"name": "Euro10", "type": "item", "sortid": 55, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_10", "path": "~/Economy-System/Money"}, {"name": "Euro20", "type": "item", "sortid": 56, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_20", "path": "~/Economy-System/Money"}, {"name": "Euro50", "type": "item", "sortid": 57, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_50", "path": "~/Economy-System/Money"}, {"name": "Euro100", "type": "item", "sortid": 58, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_100", "path": "~/Economy-System/Money"}, {"name": "Euro200", "type": "item", "sortid": 59, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_200", "path": "~/Economy-System/Money"}, {"name": "Euro500", "type": "item", "sortid": 60, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "euro_500", "path": "~/Economy-System/Money"}, {"name": "Ein<PERSON><PERSON><PERSON>", "type": "procedure", "sortid": 81, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "e<PERSON>za<PERSON>en", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Economy-System"}, {"name": "EcoGlobal", "type": "code", "sortid": 82, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "eco_global", "path": "~/Economy-System"}, {"name": "Au<PERSON>ahl<PERSON>", "type": "procedure", "sortid": 83, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "auszahl<PERSON>", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "procedure", "sortid": 84, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "uberweisen", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Economy-System"}, {"name": "SetHausData", "type": "procedure", "sortid": 85, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "set_haus_data", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Haus-System"}, {"name": "GetHausData", "type": "procedure", "sortid": 86, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "get_haus_data", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Haus-System"}, {"name": "KartenGUI", "type": "gui", "sortid": 88, "compiles": true, "locked_code": false, "ids": {"0": 13}, "registry_name": "karten_gui", "path": "~/Economy-System"}, {"name": "KartenGUI2", "type": "gui", "sortid": 89, "compiles": true, "locked_code": false, "ids": {"0": 14}, "registry_name": "karten_gui_2", "path": "~/Economy-System"}, {"name": "Nocha<PERSON>", "type": "code", "sortid": 90, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "nochat", "path": "~/RP-<PERSON>uff"}, {"name": "KartenleserItem", "type": "item", "sortid": 49, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "kartenleser_item", "path": "~/newblocks"}, {"name": "CarSpawnerGUI", "type": "gui", "sortid": 91, "compiles": true, "locked_code": false, "ids": {"0": 16}, "registry_name": "car_spawner_gui", "path": "~/Car_Spawner"}, {"name": "CarSpawnerCreateGUI", "type": "gui", "sortid": 92, "compiles": true, "locked_code": false, "ids": {"0": 17}, "registry_name": "car_spawner_create_gui", "path": "~/Car_Spawner"}, {"name": "CarSpawnerOnBlockRightClicked", "type": "procedure", "sortid": 93, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "car_spawner_on_block_right_clicked", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Car_Spawner"}, {"name": "CarSpawner", "type": "block", "sortid": 22, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "car_spawner", "path": "~/newblocks"}, {"name": "SpawnCar", "type": "procedure", "sortid": 94, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "spawn_car", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Car_Spawner"}, {"name": "SetCarSpawner", "type": "procedure", "sortid": 95, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "set_car_spawner", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Car_Spawner"}, {"name": "PinBoardGUI", "type": "gui", "sortid": 96, "compiles": true, "locked_code": false, "ids": {"0": 18}, "registry_name": "pin_board_gui", "path": "~/Haus-System"}, {"name": "Inspectcommand", "type": "command", "sortid": 97, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "inspectcommand", "path": "~/Haus-System"}, {"name": "InventarDisplayOverlayIngame", "type": "procedure", "sortid": 98, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "inventar_display_overlay_ingame", "metadata": {"return_type": "LOGIC", "dependencies": [{"name": "entity", "type": "entity"}, {"name": "world", "type": "world"}]}, "path": "~/Haus-System"}, {"name": "Inspection", "type": "procedure", "sortid": 99, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "inspection", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Haus-System"}, {"name": "Inventar", "type": "overlay", "sortid": 100, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "inventar", "path": "~/Haus-System"}, {"name": "PayCheck", "type": "code", "sortid": 101, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "pay_check", "path": "~/Economy-System"}, {"name": "OOC", "type": "command", "sortid": 102, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "ooc", "path": "~/RP-<PERSON>uff"}, {"name": "OOCGLOBAL", "type": "command", "sortid": 103, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "oocglobal", "path": "~/RP-<PERSON>uff"}, {"name": "Eye", "type": "procedure", "sortid": 104, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "eye", "metadata": {"return_type": "LOGIC", "dependencies": [{"name": "entity", "type": "entity"}, {"name": "world", "type": "world"}]}, "path": "~/Haus-System"}, {"name": "Eye<PERSON>", "type": "overlay", "sortid": 105, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "eyeo", "path": "~/Haus-System"}, {"name": "Tagnamegiver", "type": "procedure", "sortid": 106, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "tagnamegiver", "metadata": {"dependencies": []}, "path": "~/Car_Spawner"}, {"name": "CarDespawnerGUI", "type": "gui", "sortid": 108, "compiles": true, "locked_code": false, "ids": {"0": 21}, "registry_name": "car_despawner_gui", "path": "~/Car_Spawner"}, {"name": "CarDespawn", "type": "procedure", "sortid": 109, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "car_despawn", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Car_Spawner"}, {"name": "Garage", "type": "block", "sortid": 23, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "garage", "path": "~/newblocks"}, {"name": "Ausparken", "type": "procedure", "sortid": 110, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "ausparken", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Car_Spawner"}, {"name": "GarageSave", "type": "procedure", "sortid": 111, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "garage_save", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Car_Spawner"}, {"name": "GarageSaveGUI", "type": "gui", "sortid": 112, "compiles": true, "locked_code": false, "ids": {"0": 22}, "registry_name": "garage_save_gui", "path": "~/Car_Spawner"}, {"name": "Kartenleserpickup", "type": "procedure", "sortid": 113, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "ka<PERSON>leserpic<PERSON><PERSON>", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/newblocks"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "procedure", "sortid": 114, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "ka<PERSON><PERSON><PERSON><PERSON>", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/newblocks"}, {"name": "GarageClicked", "type": "procedure", "sortid": 115, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "garage_clicked", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Car_Spawner"}, {"name": "SetGarage", "type": "procedure", "sortid": 116, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "set_garage", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Car_Spawner"}, {"name": "CordsGUI", "type": "gui", "sortid": 117, "compiles": true, "locked_code": false, "ids": {"0": 23}, "registry_name": "cords_gui", "path": "~/Car_Spawner"}, {"name": "VRSRemover", "type": "item", "sortid": 36, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "vrs_remover", "path": "~/Car_Spawner"}, {"name": "SetBetrag", "type": "procedure", "sortid": 118, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "set_betrag", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System"}, {"name": "Setbetraggui", "type": "procedure", "sortid": 119, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "set<PERSON>rag<PERSON><PERSON>", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "guistate", "type": "map"}]}, "path": "~/Economy-System"}, {"name": "Dynamxkill", "type": "command", "sortid": 120, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "dynamxkill", "path": "~/Car_Spawner"}, {"name": "Payup", "type": "procedure", "sortid": 122, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "payup", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System"}, {"name": "B1E", "type": "procedure", "sortid": 125, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_1_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "B10E", "type": "procedure", "sortid": 126, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_10_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "B100E", "type": "procedure", "sortid": 127, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_100_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "B2E", "type": "procedure", "sortid": 128, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_2_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "B20E", "type": "procedure", "sortid": 129, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_20_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "B200E", "type": "procedure", "sortid": 130, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_200_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "B5E", "type": "procedure", "sortid": 131, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_5_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "B50E", "type": "procedure", "sortid": 132, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_50_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "B500E", "type": "procedure", "sortid": 133, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "b_500_e", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "ATMGUIThisGUIIsClosed", "type": "procedure", "sortid": 134, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "atmgui_this_gui_is_closed", "metadata": {"dependencies": [{"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/newblocks"}, {"name": "Clearmoney", "type": "procedure", "sortid": 135, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "clearmoney", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Economy-System/Money"}, {"name": "StartCommands", "type": "code", "sortid": 137, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "start_commands", "path": "~/Dateiverwaltung"}, {"name": "StartCommandsJoin", "type": "code", "sortid": 138, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "start_commands_join", "path": "~/Dateiverwaltung"}, {"name": "<PERSON><PERSON>", "type": "command", "sortid": 139, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "carset", "path": "~/Car_Spawner"}, {"name": "<PERSON>", "type": "command", "sortid": 140, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "kit", "path": "~/RP-<PERSON>uff"}, {"name": "FancyMenuForce", "type": "code", "sortid": 141, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "fancy_menu_force", "path": "~/Dateiverwaltung"}, {"name": "Gm", "type": "command", "sortid": 142, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "gm", "path": "~/Essentials"}, {"name": "Warp", "type": "command", "sortid": 143, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "warp", "path": "~/Essentials/warps"}, {"name": "<PERSON><PERSON><PERSON>", "type": "command", "sortid": 144, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "setwarp", "path": "~/Essentials/warps"}, {"name": "Warprq", "type": "command", "sortid": 145, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "warprq", "path": "~/Essentials/warps"}, {"name": "Warps", "type": "command", "sortid": 146, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "warps", "path": "~/Essentials/warps"}, {"name": "Ec", "type": "command", "sortid": 148, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "ec", "path": "~/Essentials"}, {"name": "Clear", "type": "command", "sortid": 149, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "clear", "path": "~/Essentials"}, {"name": "God", "type": "command", "sortid": 150, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "god", "path": "~/Essentials"}, {"name": "Fly", "type": "command", "sortid": 151, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "fly", "path": "~/Essentials"}, {"name": "Skull", "type": "command", "sortid": 152, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "skull", "path": "~/Essentials"}, {"name": "T<PERSON><PERSON>", "type": "command", "sortid": 153, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tpall", "path": "~/Essentials"}, {"name": "Head", "type": "command", "sortid": 154, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "head", "path": "~/Essentials"}, {"name": "Day", "type": "command", "sortid": 155, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "day", "path": "~/Essentials"}, {"name": "Night", "type": "command", "sortid": 156, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "night", "path": "~/Essentials"}, {"name": "Sun", "type": "command", "sortid": 157, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "sun", "path": "~/Essentials"}, {"name": "Rain", "type": "command", "sortid": 158, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "rain", "path": "~/Essentials"}, {"name": "Thunder", "type": "command", "sortid": 159, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "thunder", "path": "~/Essentials"}, {"name": "Heal", "type": "command", "sortid": 164, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "heal", "path": "~/Essentials"}, {"name": "Feed", "type": "command", "sortid": 165, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "feed", "path": "~/Essentials"}, {"name": "<PERSON><PERSON>", "type": "command", "sortid": 166, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "rename", "path": "~/Essentials"}, {"name": "ChatColor", "type": "code", "sortid": 167, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "chat_color", "path": "~/Tests"}, {"name": "Addlore", "type": "command", "sortid": 168, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "addlore", "path": "~/Essentials"}, {"name": "Setlore", "type": "command", "sortid": 169, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "setlore", "path": "~/Essentials"}, {"name": "RemoveLore", "type": "command", "sortid": 170, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "remove_lore", "path": "~/Essentials"}, {"name": "Set<PERSON>aw<PERSON>", "type": "command", "sortid": 171, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "setspawn", "path": "~/Essentials"}, {"name": "Spawn", "type": "command", "sortid": 172, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "spawn", "path": "~/Essentials"}, {"name": "Tpaaccept", "type": "command", "sortid": 173, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tpaaccept", "path": "~/Essentials"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "command", "sortid": 174, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tpadeny", "path": "~/Essentials/tpa-system"}, {"name": "<PERSON><PERSON><PERSON>", "type": "command", "sortid": 175, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tpaall", "path": "~/Essentials/tpa-system"}, {"name": "Tpa", "type": "command", "sortid": 176, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tpa", "path": "~/Essentials"}, {"name": "Tpahere", "type": "command", "sortid": 177, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tpahere", "path": "~/Essentials/tpa-system"}, {"name": "Setview", "type": "command", "sortid": 178, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "setview", "path": "~/Essentials"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "command", "sortid": 179, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "registerausweis", "path": "~/ID-System"}, {"name": "Nbtest", "type": "command", "sortid": 180, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "nbtest", "path": "~/Tests"}, {"name": "Hunger", "type": "command", "sortid": 182, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "hunger", "path": "~/Essentials"}, {"name": "Unbreakable", "type": "command", "sortid": 183, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "unbreakable", "path": "~/Essentials"}, {"name": "Broadcast", "type": "command", "sortid": 184, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "broadcast", "path": "~/Essentials"}, {"name": "SpawnJoin", "type": "code", "sortid": 185, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "spawn_join", "path": "~/Essentials"}, {"name": "<PERSON><PERSON>", "type": "command", "sortid": 186, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "sudo", "path": "~/Essentials"}, {"name": "Tphere", "type": "command", "sortid": 187, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tphere", "path": "~/Essentials"}, {"name": "Trashcan", "type": "gui", "sortid": 188, "compiles": true, "locked_code": true, "ids": {"0": 36}, "registry_name": "trashcan", "path": "~/Essentials"}, {"name": "Trash", "type": "command", "sortid": 189, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "trash", "path": "~/Essentials"}, {"name": "<PERSON><PERSON><PERSON>", "type": "command", "sortid": 191, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "notruf", "path": "~/Discord"}, {"name": "PoliceRanks", "type": "code", "sortid": 198, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "police_ranks", "path": "~/Dateiverwaltung"}, {"name": "Ausweisw", "type": "item", "sortid": 48, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "ausweisw", "path": "~/ID-System"}, {"name": "Dokumente", "type": "item", "sortid": 61, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "dokumente", "path": "~/newblocks"}, {"name": "<PERSON><PERSON><PERSON>", "type": "code", "sortid": 199, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "job_main", "path": "~/Job-System"}, {"name": "Job", "type": "command", "sortid": 200, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "job", "path": "~/Job-System"}, {"name": "JobGUI", "type": "gui", "sortid": 201, "compiles": true, "locked_code": true, "ids": {"0": 39}, "registry_name": "job_gui", "path": "~/Job-System"}, {"name": "JobBlock", "type": "block", "sortid": 24, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "job_block", "path": "~/Job-System"}, {"name": "JobBlockOnBlockRightClicked", "type": "procedure", "sortid": 202, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "job_block_on_block_right_clicked", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Job-System"}, {"name": "AusweisGlobal", "type": "code", "sortid": 203, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "ausweis_global", "path": "~/ID-System"}, {"name": "TestGui", "type": "code", "sortid": 205, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "test_gui", "path": "~/Tests"}, {"name": "TestMod", "type": "code", "sortid": 206, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "test_mod", "path": "~/Tests"}, {"name": "Worldguardsetup", "type": "command", "sortid": 207, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "worldguardsetup", "path": "~/worldguard"}, {"name": "DownloadLib", "type": "code", "sortid": 209, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "download_lib", "path": "~/Dateiverwaltung"}, {"name": "OptifineCheck", "type": "code", "sortid": 211, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "optifine_check", "path": "~/Dateiverwaltung"}, {"name": "Checkversion", "type": "command", "sortid": 212, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "checkversion", "path": "~/Tests"}, {"name": "TestFTPDownload", "type": "code", "sortid": 213, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "test_ftp_download", "path": "~/Dateiverwaltung"}, {"name": "Vanish", "type": "command", "sortid": 214, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "vanish", "path": "~/Essentials"}, {"name": "ProtectItemSystem", "type": "code", "sortid": 215, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "protect_item_system", "path": "~/Security"}, {"name": "Blockitem", "type": "command", "sortid": 216, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "blockitem", "path": "~/Security"}, {"name": "ProtectItemListener", "type": "code", "sortid": 217, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "protect_item_listener", "path": "~/Security"}, {"name": "MineRettungLogo", "type": "block", "sortid": 38, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "mine_rettung_logo", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "Ctoggle", "type": "command", "sortid": 220, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "ctoggle", "path": "~/Tests"}, {"name": "RoutesMain", "type": "code", "sortid": 221, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "routes_main", "path": "~/Routes-System"}, {"name": "RoutesCommand", "type": "command", "sortid": 222, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "routes_command", "path": "~/Routes-System"}, {"name": "ATM2", "type": "block", "sortid": 225, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "atm_2", "path": "~/newblocks"}, {"name": "ClickCommandsStart", "type": "code", "sortid": 226, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "click_commands_start", "path": "~/ClickCommands"}, {"name": "Setclickaction", "type": "command", "sortid": 227, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "setclickaction", "path": "~/ClickCommands"}, {"name": "Deleteclickaction", "type": "command", "sortid": 228, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "deleteclickaction", "path": "~/ClickCommands"}, {"name": "ClickBlockListener", "type": "code", "sortid": 229, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "click_block_listener", "path": "~/ClickCommands"}, {"name": "ATMGUIBackup", "type": "gui", "sortid": 230, "compiles": true, "locked_code": false, "ids": {"0": 42}, "registry_name": "atmgui_backup", "path": "~/Economy-System"}, {"name": "ATM3", "type": "block", "sortid": 231, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "atm_3", "path": "~/newblocks"}, {"name": "Getcard", "type": "procedure", "sortid": 235, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "getcard", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Tests"}, {"name": "InteractionPlayer", "type": "procedure", "sortid": 237, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "interaction_player", "metadata": {"dependencies": []}, "path": "~/Tests"}, {"name": "<PERSON><PERSON>", "type": "procedure", "sortid": 246, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "funf", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Tests"}, {"name": "<PERSON><PERSON>", "type": "procedure", "sortid": 245, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "vier", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Tests"}, {"name": "<PERSON><PERSON>", "type": "procedure", "sortid": 239, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "eins", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Tests"}, {"name": "Readingcards", "type": "procedure", "sortid": 242, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "readingcards", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Tests"}, {"name": "Zwei", "type": "procedure", "sortid": 243, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "zwei", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Tests"}, {"name": "Card", "type": "block", "sortid": 238, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "card", "path": "~/Tests"}, {"name": "Cardreader", "type": "block", "sortid": 240, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "cardreader", "path": "~/Tests"}, {"name": "<PERSON><PERSON>", "type": "procedure", "sortid": 244, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "drei", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Tests"}, {"name": "Givecardinfo", "type": "gui", "sortid": 236, "compiles": true, "locked_code": false, "ids": {"0": 46}, "registry_name": "givecardinfo", "path": "~/Tests"}, {"name": "<PERSON><PERSON>", "type": "gui", "sortid": 233, "compiles": true, "locked_code": false, "ids": {"0": 44}, "registry_name": "gui", "path": "~/Tests"}, {"name": "CardAssignment", "type": "gui", "sortid": 234, "compiles": true, "locked_code": false, "ids": {"0": 45}, "registry_name": "card_assignment", "path": "~/Tests"}, {"name": "Se<PERSON>", "type": "procedure", "sortid": 247, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "sechs", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Tests"}, {"name": "Cardreading", "type": "gui", "sortid": 241, "compiles": true, "locked_code": false, "ids": {"0": 47}, "registry_name": "cardreading", "path": "~/Tests"}, {"name": "MWBuildingBlocks", "type": "tab", "sortid": 248, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "mw_building_blocks", "path": "~/CreativeTabs"}, {"name": "Sleep", "type": "command", "sortid": 250, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "sleep", "path": "~/SchlafSystem"}, {"name": "SleepFinal", "type": "procedure", "sortid": 251, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "sleep_final", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/SchlafSystem"}, {"name": "ExitGui", "type": "procedure", "sortid": 252, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "exit_gui", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}]}, "path": "~/Global"}, {"name": "EinschlafenGUi", "type": "gui", "sortid": 253, "compiles": true, "locked_code": true, "ids": {"0": 49}, "registry_name": "einschlafen_g_ui", "path": "~/SchlafSystem"}, {"name": "KMC", "type": "block", "sortid": 254, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "kmc", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "AusweisInfos", "type": "gui", "sortid": 255, "compiles": true, "locked_code": true, "ids": {"0": 50}, "registry_name": "ausweis_infos", "path": "~/ID-System"}, {"name": "AusweisInfoClick", "type": "procedure", "sortid": 256, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "ausweis_info_click", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/ID-System"}, {"name": "SignTest", "type": "block", "sortid": 257, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "sign_test", "path": "~/Tests"}, {"name": "TeamBase", "type": "code", "sortid": 258, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "team_base", "path": "~/Teams"}, {"name": "Team", "type": "command", "sortid": 259, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "team", "path": "~/Teams"}, {"name": "DerTest", "type": "gui", "sortid": 260, "compiles": true, "locked_code": true, "ids": {"0": 59}, "registry_name": "der_test", "path": "~/Tests/GUISlotClickTest"}, {"name": "Open", "type": "procedure", "sortid": 261, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "open", "metadata": {"dependencies": []}, "path": "~/Tests/GUISlotClickTest"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "procedure", "sortid": 262, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "cuff_use", "metadata": {"dependencies": []}, "path": "~/newblocks"}, {"name": "Mwworldcopytool", "type": "item", "sortid": 263, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "mwworldcopytool", "path": "~/MW-World-Copy"}, {"name": "Mwcopy", "type": "command", "sortid": 264, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "mwcopy", "path": "~/MW-World-Copy"}, {"name": "GCFolder", "type": "code", "sortid": 265, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "gc_folder", "path": "~/GUI Creator"}, {"name": "MwPathUpdate", "type": "code", "sortid": 266, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "mw_path_update"}, {"name": "Blockregen", "type": "command", "sortid": 271, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "blockregen", "path": "~/Block-Regen"}, {"name": "NoSnow", "type": "procedure", "sortid": 274, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "no_snow", "metadata": {"dependencies": [{"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Global"}, {"name": "Delete", "type": "command", "sortid": 276, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "delete", "path": "~/Essentials"}, {"name": "Replacemode", "type": "command", "sortid": 277, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "replacemode", "path": "~/Essentials"}, {"name": "ReplaceTool", "type": "item", "sortid": 278, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "replace_tool", "path": "~/MW-World-Copy"}, {"name": "Replacemodeundo", "type": "command", "sortid": 279, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "replacemodeundo", "path": "~/Essentials"}, {"name": "Inv", "type": "command", "sortid": 280, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "inv", "path": "~/Essentials"}, {"name": "F3Hotkey", "type": "keybind", "sortid": 283, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "f_3_hotkey", "path": "~/Hotkeys"}, {"name": "F1Hotkey", "type": "keybind", "sortid": 282, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "f_1_hotkey", "path": "~/Hotkeys"}, {"name": "InventarOpen", "type": "command", "sortid": 284, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "inventar_open", "path": "~/Essentials"}, {"name": "Glow", "type": "command", "sortid": 283, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "glow", "path": "~/Essentials"}, {"name": "Home", "type": "command", "sortid": 282, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "home", "path": "~/Essentials"}, {"name": "Prop", "type": "command", "sortid": 285, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "prop", "path": "~/PROP system"}, {"name": "NDynamx", "type": "command", "sortid": 287, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "n_dynamx", "path": "~/PROP system"}, {"name": "Tempban", "type": "command", "sortid": 285, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tempban", "path": "~/Essentials"}, {"name": "AbschleppTool", "type": "item", "sortid": 288, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "abschlepp_tool", "path": "~/newblocks"}, {"name": "Timespeed", "type": "command", "sortid": 290, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "timespeed", "path": "~/Essentials"}, {"name": "UnbanIP", "type": "command", "sortid": 287, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "unban_ip", "path": "~/Essentials"}, {"name": "BanIP", "type": "command", "sortid": 286, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "ban_ip", "path": "~/Essentials"}, {"name": "Tpoffline", "type": "command", "sortid": 289, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "tpoffline", "path": "~/Essentials"}, {"name": "Back", "type": "command", "sortid": 291, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "back", "path": "~/Essentials"}, {"name": "DynamXCarKill", "type": "code", "sortid": 292, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "dynam_x_car_kill", "path": "~/Tests"}, {"name": "DiscordBotStart", "type": "code", "sortid": 293, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "discord_bot_start", "path": "~/Discord/Discord-<PERSON><PERSON>"}, {"name": "DiscordJoinMessage", "type": "code", "sortid": 294, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "discord_join_message", "path": "~/Discord/Discord-<PERSON><PERSON>"}, {"name": "DiscordLeaveMessage", "type": "code", "sortid": 295, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "discord_leave_message", "path": "~/Discord/Discord-<PERSON><PERSON>"}, {"name": "DiscordNotrufSend", "type": "code", "sortid": 296, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "discord_notruf_send", "path": "~/Discord/Discord-<PERSON><PERSON>"}, {"name": "NewDiscordRPC", "type": "code", "sortid": 297, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "new_discord_rpc", "path": "~/Discord/RPC"}, {"name": "DiscordEventHandlers", "type": "code", "sortid": 298, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "discord_event_handlers", "path": "~/Discord/RPC"}, {"name": "DiscordRPC", "type": "code", "sortid": 299, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "discord_rpc", "path": "~/Discord/RPC"}, {"name": "DiscordRichPresence", "type": "code", "sortid": 300, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "discord_rich_presence", "path": "~/Discord/RPC"}, {"name": "DiscordUser", "type": "code", "sortid": 301, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "discord_user", "path": "~/Discord/RPC"}, {"name": "Guicreator", "type": "command", "sortid": 302, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "guicreator", "path": "~/GUI Creator"}, {"name": "Propprefix", "type": "command", "sortid": 304, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "propprefix", "path": "~/PROP system"}, {"name": "Propprefixconfig", "type": "code", "sortid": 305, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "propprefixconfig", "path": "~/PROP system"}, {"name": "Ausweistest", "type": "command", "sortid": 306, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "ausweistest", "path": "~/Global"}, {"name": "Navi", "type": "command", "sortid": 310, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "navi", "path": "~/Navi"}, {"name": "CommandManager", "type": "code", "sortid": 303, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "command_manager", "path": "~/Discord/Discord-Bot/MW Discord System"}, {"name": "NaviCords", "type": "command", "sortid": 311, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "navi_cords", "path": "~/Navi"}, {"name": "JavaJDBC", "type": "code", "sortid": 304, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "java_jdbc", "path": "~/Discord/Discord-Bot/MW Discord System"}, {"name": "Test", "type": "code", "sortid": 307, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "test", "path": "~/Tests/Animations"}, {"name": "Delwarp", "type": "command", "sortid": 308, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "<PERSON><PERSON><PERSON>", "path": "~/Essentials/warps"}, {"name": "ExitSign", "type": "block", "sortid": 312, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "exit_sign", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "<PERSON><PERSON><PERSON>", "type": "command", "sortid": 313, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "navia<PERSON>", "path": "~/Navi"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "command", "sortid": 314, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "navir<PERSON><PERSON>", "path": "~/Navi"}, {"name": "ForSaleSignBlockAdded", "type": "procedure", "sortid": 316, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "for_sale_sign_block_added", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Haus-System"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "procedure", "sortid": 317, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "sell_main", "metadata": {"dependencies": [{"name": "entity", "type": "entity"}, {"name": "x", "type": "number"}, {"name": "y", "type": "number"}, {"name": "z", "type": "number"}, {"name": "world", "type": "world"}]}, "path": "~/Haus-System"}, {"name": "DrehstartCommand", "type": "code", "sortid": 318, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "drehstart_command", "path": "~/Discord/Discord-<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "type": "command", "sortid": 319, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "navistop", "path": "~/Navi"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "block", "sortid": 320, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "tattoo_chair", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "TattooChair<PERSON><PERSON><PERSON>", "type": "block", "sortid": 321, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "tattoo_chair_white", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "SmoothShift", "type": "code", "sortid": 322, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "smooth_shift", "path": "~/Tests"}, {"name": "FlyDown", "type": "keybind", "sortid": 323, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "fly_down", "path": "~/Hotkeys"}, {"name": "KeyBindingFlyDown", "type": "code", "sortid": 324, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "key_binding_fly_down", "path": "~/Tests"}, {"name": "ReplaceToolMain", "type": "code", "sortid": 325, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "replace_tool_main", "path": "~/ReplaceTool"}, {"name": "Mwset", "type": "command", "sortid": 326, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "mwset", "path": "~/ReplaceTool"}, {"name": "<PERSON><PERSON><PERSON>", "type": "command", "sortid": 327, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "mwundo", "path": "~/ReplaceTool"}, {"name": "<PERSON><PERSON><PERSON>", "type": "command", "sortid": 328, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "mwredo", "path": "~/ReplaceTool"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "command", "sortid": 329, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "mwreplace", "path": "~/ReplaceTool"}, {"name": "UndoRedoHandler", "type": "code", "sortid": 330, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "undo_redo_handler", "path": "~/ReplaceTool"}, {"name": "Barstool", "type": "block", "sortid": 331, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "barstool", "path": "~/ Vees ModernProps"}, {"name": "Barrel", "type": "block", "sortid": 332, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "barrel", "path": "~/ Vees ModernProps"}, {"name": "Bench", "type": "block", "sortid": 333, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "bench", "path": "~/ Vees ModernProps"}, {"name": "Bigbox", "type": "block", "sortid": 334, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "bigbox", "path": "~/ Vees ModernProps"}, {"name": "Box", "type": "block", "sortid": 335, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "box", "path": "~/ Vees ModernProps"}, {"name": "Boxshelf", "type": "block", "sortid": 336, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "boxshelf", "path": "~/ Vees ModernProps"}, {"name": "Crate", "type": "block", "sortid": 337, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "crate", "path": "~/ Vees ModernProps"}, {"name": "Filingcabinet", "type": "block", "sortid": 338, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "filingcabinet", "path": "~/ Vees ModernProps"}, {"name": "Garbagebin", "type": "block", "sortid": 339, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "garbagebin", "path": "~/ Vees ModernProps"}, {"name": "Gundisplay", "type": "block", "sortid": 340, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "gundisplay", "path": "~/ Vees ModernProps"}, {"name": "Kitchencabinet", "type": "block", "sortid": 342, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "kitchencabinet", "path": "~/ Vees ModernProps"}, {"name": "Kitchensink", "type": "block", "sortid": 343, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "kitchensink", "path": "~/ Vees ModernProps"}, {"name": "<PERSON><PERSON>", "type": "block", "sortid": 344, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "lamp", "path": "~/ Vees ModernProps"}, {"name": "<PERSON><PERSON><PERSON>", "type": "block", "sortid": 345, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "medicalcrate", "path": "~/ Vees ModernProps"}, {"name": "Militarycrate", "type": "block", "sortid": 346, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "militarycrate", "path": "~/ Vees ModernProps"}, {"name": "Obstacle", "type": "block", "sortid": 347, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "obstacle", "path": "~/ Vees ModernProps"}, {"name": "Officechair", "type": "block", "sortid": 348, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "officechair", "path": "~/ Vees ModernProps"}, {"name": "Openbox", "type": "block", "sortid": 349, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "openbox", "path": "~/ Vees ModernProps"}, {"name": "<PERSON><PERSON><PERSON>", "type": "block", "sortid": 350, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "pallet", "path": "~/ Vees ModernProps"}, {"name": "Palletslab", "type": "block", "sortid": 351, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "palletslab", "path": "~/ Vees ModernProps"}, {"name": "Plantvase1", "type": "block", "sortid": 352, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "plantvase_1", "path": "~/ Vees ModernProps"}, {"name": "Plantvase2", "type": "block", "sortid": 353, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "plantvase_2", "path": "~/ Vees ModernProps"}, {"name": "Plantvase3", "type": "block", "sortid": 354, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "plantvase_3", "path": "~/ Vees ModernProps"}, {"name": "Plantvase4", "type": "block", "sortid": 355, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "plantvase_4", "path": "~/ Vees ModernProps"}, {"name": "Plantvase5", "type": "block", "sortid": 356, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "plantvase_5", "path": "~/ Vees ModernProps"}, {"name": "Smallboxshelf", "type": "block", "sortid": 357, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "smallboxshelf", "path": "~/ Vees ModernProps"}, {"name": "Smallboxes", "type": "block", "sortid": 358, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "smallboxes", "path": "~/ Vees ModernProps"}, {"name": "Spotlight", "type": "block", "sortid": 359, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "spotlight", "path": "~/ Vees ModernProps"}, {"name": "Storeshelf", "type": "block", "sortid": 360, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "storeshelf", "path": "~/ Vees ModernProps"}, {"name": "Storeshelfvariant", "type": "block", "sortid": 361, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "storeshelfvariant", "path": "~/ Vees ModernProps"}, {"name": "Table", "type": "block", "sortid": 362, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "table", "path": "~/ Vees ModernProps"}, {"name": "Vendingmachine", "type": "block", "sortid": 363, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "vendingmachine", "path": "~/ Vees ModernProps"}, {"name": "White<PERSON>resh<PERSON>", "type": "block", "sortid": 364, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "whitestoreshelf", "path": "~/ Vees ModernProps"}, {"name": "Whitewoodenchair", "type": "block", "sortid": 365, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "whitewoodenchair", "path": "~/ Vees ModernProps"}, {"name": "Woodbox", "type": "block", "sortid": 366, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "woodbox", "path": "~/ Vees ModernProps"}, {"name": "Woodwallshelves", "type": "block", "sortid": 367, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "woodwallshelves", "path": "~/ Vees ModernProps"}, {"name": "<PERSON><PERSON>chair", "type": "block", "sortid": 368, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "woodenchair", "path": "~/ Vees ModernProps"}, {"name": "Dynamxkillprops", "type": "command", "sortid": 369, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "dynamxkillprops", "path": "~/Tests"}, {"name": "Dynamxkillvehicles", "type": "command", "sortid": 370, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "dynamxkillvehicles", "path": "~/Tests"}, {"name": "ChestGUI", "type": "gui", "sortid": 371, "compiles": true, "locked_code": false, "ids": {"0": 65}, "registry_name": "chest_gui", "path": "~/ Vees ModernProps"}, {"name": "MwPackProtect", "type": "code", "sortid": 372, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "mw_pack_protect", "path": "~/Security"}, {"name": "Clean", "type": "command", "sortid": 373, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "clean", "path": "~/Essentials"}, {"name": "RoadUpdater", "type": "item", "sortid": 374, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "road_updater", "path": "~/ReplaceTool"}, {"name": "IronFenceEdge", "type": "block", "sortid": 375, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "iron_fence_edge", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "IronFence", "type": "block", "sortid": 376, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "iron_fence", "path": "~/newblocks/MWBuildingBlocks"}, {"name": "<PERSON><PERSON>", "type": "command", "sortid": 377, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "cleanh", "path": "~/Essentials"}, {"name": "ChangeKeyGMSwitch", "type": "code", "sortid": 378, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "change_key_gm_switch", "path": "~/Tests"}, {"name": "Gmforce", "type": "command", "sortid": 379, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "gmforce", "path": "~/Essentials"}, {"name": "GmforceV", "type": "command", "sortid": 380, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "gmforce_v", "path": "~/Essentials"}, {"name": "SnwoRemover", "type": "item", "sortid": 381, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "snwo_remover", "path": "~/ReplaceTool"}, {"name": "Hudc", "type": "command", "sortid": 382, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "hudc", "path": "~/Essentials"}, {"name": "GUICommand", "type": "command", "sortid": 383, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "gui_command", "path": "~/Tests/gctest"}, {"name": "GuiCustom", "type": "code", "sortid": 384, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "gui_custom", "path": "~/Tests/gctest"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "code", "sortid": 386, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "gui_handler", "path": "~/Tests/gctest"}, {"name": "Skin", "type": "command", "sortid": 387, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "skin", "path": "~/Tests"}, {"name": "Vehiclepreset", "type": "command", "sortid": 388, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "vehiclepreset", "path": "~/Essentials"}, {"name": "Map", "type": "gui", "sortid": 389, "compiles": true, "locked_code": false, "ids": {"0": 66}, "registry_name": "map", "path": "~/Tests"}, {"name": "OpenMap", "type": "command", "sortid": 390, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "open_map", "path": "~/Tests"}, {"name": "Ftp", "type": "command", "sortid": 391, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "ftp"}, {"name": "Clientmap", "type": "code", "sortid": 392, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "clientmap", "path": "~/Tests"}, {"name": "LangNetworkHandler", "type": "code", "sortid": 394, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "lang_network_handler", "path": "~/Network"}, {"name": "HudNetworkHandler", "type": "code", "sortid": 395, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "hud_network_handler", "path": "~/Network"}, {"name": "DefaultOptions", "type": "code", "sortid": 396, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "default_options", "path": "~/Dateiverwaltung"}, {"name": "PngNetworkHandler", "type": "code", "sortid": 396, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "png_network_handler", "path": "~/Network"}, {"name": "Fingerprintscanner", "type": "block", "sortid": 397, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "fingerprintscanner", "path": "~/newblocks/Fingerprint scanner"}, {"name": "Nodrop", "type": "command", "sortid": 398, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "nodrop", "path": "~/Essentials"}, {"name": "<PERSON><PERSON>", "type": "command", "sortid": 399, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "registerid", "path": "~/ID-System"}, {"name": "Baton", "type": "tool", "sortid": 400, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "baton", "path": "~/Polizei"}, {"name": "Taser", "type": "item", "sortid": 401, "compiles": true, "locked_code": true, "ids": {}, "registry_name": "taser", "path": "~/Polizei"}, {"name": "TaserAmmo", "type": "item", "sortid": 402, "compiles": true, "locked_code": false, "ids": {}, "registry_name": "taser_ammo", "path": "~/Polizei"}], "variable_elements": [], "sound_elements": [{"name": "klappe", "files": ["1167"], "category": "master", "subtitle": "Und Aktion"}, {"name": "radio2", "files": ["ptt3"], "category": "master", "subtitle": ""}, {"name": "radio", "files": ["ptt2"], "category": "master", "subtitle": ""}, {"name": "cuffsound", "files": ["cuffsound"], "category": "master", "subtitle": ""}, {"name": "no_route", "files": ["keine_route"], "category": "master", "subtitle": ""}, {"name": "doorscan", "files": ["doorscan"], "category": "block", "subtitle": ""}], "language_map": {"en_us": {"command.tpall.teleported": "You have been teleported to %s.", "command.invsee.player_not_found": "Player not found: %s", "tile.tanksaule.name": "Zapfs�ule", "command.night.success": "The time of day has been set to night.", "command.warp.player_not_found": "Player %s not found.", "command.rain.argument_error": "This command does not support arguments.", "command.warp.teleport_other_success": "You have teleported %s to warp: %s", "message.addlore.noitem": "Hold an item in your hand to add lore!", "key.mcreator.hands_up": "Hands Up", "command.setlore.error_old_lore_not_found": "The old lore was not found!", "command.inv.save.success": "Inventory saved as %s.", "item.euro_5.name": "F�nf Euro", "command.vehiclepreset.invalid_arguments": "Invalid arguments! Usage: /vehiclepreset <vehicleName> <licensePlate>", "command.tpaall.usage": "Usage: /tpaall", "command.replacemode.success": "Replacemode set to %d", "command.delwarp.usage": "Usage: /delwarp <warpname>", "tile.boxshelf.name": "Box shelf", "command.hunger.all.success": "The hunger levels of all players have been set to half.", "command.rain.usage": "/rain", "command.heal.by": "You have been healed by %s.", "item.road_updater.name": "Road Updater", "tile.full_grass_block.name": "Full Grass Block", "command.inv.save.error": "Failed to save inventory: %s", "command.warprq.broadcast_sent": "Warp request sent to all players except yourself.", "command.warps.error_reading": "Error reading warp information.", "command.feed.success_target": "Player %s feeded", "item.radio.name": "Radio", "tile.woodwallshelves.name": "Wood wall shelves", "item.fahrzeugschein_used.name": "Fahrzeugschein", "tile.new_gravel.name": "New Gravel", "tile.behandeltes_holz.name": "Behandeltes Holz", "command.delete.usage": "Usage: /delete <entityname>", "item.dienstausweiss.name": "Dienstausweis", "command.tpahere.cant_send_to_self": "You can't send a tpa to yourself.", "command.feed.player_not_found": "Player %s not found.", "tile.warped_trapdoor.name": "Warped Trapdoor", "command.registerid.mysql_not_connected": "MySQL is not connected, command function disabled.", "command.setspawn.success": "Spawn has been set at X: %d, Y: %d, Z: %d with yaw: %.2f and pitch: %.2f", "tile.kaffee_maschiene.name": "<PERSON><PERSON><PERSON> Ma<PERSON>e", "item.euro_1.name": "Ein Euro", "command.registerid.register_error": "Error during registration.", "item.donut.name": "Donut", "command.trash.usage": "/trash [<playername>]", "command.dynamxkillvehicles.invalid_arguments": "Invalid arguments. Usage: ", "tile.plantvase_3.name": "Plant vase", "command.tpaall.accept_button": "[ACCEPT] ", "command.tempban.no_arguments": "Usage: /tempban <player> <time>", "tile.sidewalk.name": "Sidewalk", "command.clear.player.notfound": "Player %s not found.", "tile.birch_trapdoor.name": "<PERSON>", "command.replacemode.usage": "/replacemode <number>", "command.hunger.usage": "/hunger [<playername>]", "tile.box.name": "Box", "command.replacemodeundo.no_replacements": "No replacements to undo.", "command.heal.self": "You have been healed.", "tile.grass_stone_path_2.name": "Grass Stone Path", "command.vehiclepreset.spawn_success": "Spawned vehicle: %s with plate: %s", "command.home.teleport.success": "Teleported to home '%s'.", "command.removelore.not_found": "The specified lore was not found!", "command.hunger.self.error": "Your hunger level cannot be decreased further.", "command.inv.delete.not_found": "No inventory found with name %s.", "command.night.argument_error": "This command does not support arguments.", "command.hunger.single.success": "You have set the hunger level of %s to half.", "subtitles.radio2": "", "item.dokumente.name": "Dokument", "item.coffee.name": "Coffee", "message.broadcast.usage": "Usage: /broadcast <message>", "command.feed.success_self": "Your hunger has been filled.", "command.warp.not_found": "Warp '%s' not found.", "command.tpa.usage": "/tpa [<playername>]", "message.you_can_change": "You can now change your game mode again.", "command.tpoffline.no_data": "No saved data for player: %s", "tile.barrel.name": "Barrel", "item.card_2.name": "Card Gold", "tile.plantvase_2.name": "Plant vase", "tile.kitchencabinet.name": "Kitchen Cabinet", "command.night.usage": "/night", "command.inv.save.exists": "This inv already exists", "item.euro_200.name": "Zweihundert Euro", "item.ausweis_used.name": "Ausweis", "command.setwarp.error": "Error setting warp.", "item.taser.name": "Taser", "item.replace_tool.name": "Replace Tool", "command.tpall.usage": "/tpall", "command.home.set.exists": "A home with the name '%s' already exists.", "message.usage": "Usage: /gmforcev <playername> <gamemode>", "message.clean.usage": "This command does not support arguments.", "tile.kartenleser.name": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.klappe": "And action", "command.tpall.no_permission": "You do not have permission to use this command.", "message.back.noposition": "No last position found.", "command.warprq.reject_target": "The request from %s has been rejected.", "command.tpaall.accept_hover": "Click here to accept the request.", "tile.smallboxshelf.name": "Small box shelf", "command.home.set.usage": "Usage: /home set <name>", "command.unbreakable.no_item": "The player %s has no item in hand.", "command.gm.invalid_mode": "Invalid game mode. Please use /gm 0, /gm 1, /gm 2 or /gm 3.", "tile.spotlight.name": "Spotlight", "command.invsee.not_player": "Only players can use this command.", "command.dynamxkillvehicles.available_names": "Available names: ", "command.gm.no_arguments": "No arguments provided. Please use /gm 0, /gm 1, /gm 2 or /gm 3.", "command.invsee.usage": "/invsee <playername>", "command.spawn.teleport_success": "You have been teleported to spawn.", "subtitles.radio": "", "subtitles.cuffsound": "", "command.ec.open_ender_chest": "Opened Ender Chest of %s", "tile.tattoo_chair.name": "Tattoo Chair", "command.head.self": "You have received your own head!", "command.unbreakable.standard": "The item is now unbreakable", "message.banip.kicked": "You have been permanently banned", "command.dynamxkillvehicles.with_name": " entities with name '", "tile.garage.name": "Garage", "item.card_1.name": "Card Black", "command.timespeed.invalid_time_format": "Invalid time format. Use <number><s|m|h>, e.g., 1m for 1 minute.", "tile.iron_fence_edge.name": "Iron Fence Edge", "command.removelore.success": "Lore has been successfully removed!", "command.hunger.single.error": "The hunger level of %s cannot be decreased further.", "tile.bench.name": "Bench", "item.die_minewache.name": "Die Minewache", "command.unbanip.usage": "/unbanip <IP-Adresse>", "command.setwarp.exists": "Warp '%s' already exists.", "command.tpaaccept.usage": "Usage: /tpaaccept", "command.warprq.request_sent": "Warp request sent to %s.", "command.tpaaccept.not_found": "The Player was not found or is offline.", "message.back.playeronly": "This command can only be used by players.", "tile.kasse.name": "<PERSON><PERSON>", "command.tpaaccept.no_request": "You have not received a teleportation request from someone.", "tile.spruce_trapdoor.name": "Spruce Trapdoor", "item.taser_ammo.name": "Taser Ammo", "command.feed.usage": "Invalid command usage. Use /feed [player]", "command.warp.teleport_success": "You have been teleported to warp: %s", "command.vanish.now_invisible": "You are now invisible!", "tile.pallet.name": "<PERSON><PERSON><PERSON>", "command.fly.disabled": "Fly mode disabled!", "command.hunger.others.success": "You have set the hunger level of %s to half.", "command.clear.self.success": "Your inventory has been cleared!", "command.unbanip.success": "IP address %s has been unbanned.", "command.sudo.force_command": "Forced player %s to execute the command: %s", "message.addlore.duplicate": "You cannot have two lores with the same name!", "command.home.delete.usage": "Usage: /home delete <name>", "command.removelore.usage": "/removelore [Lore]", "command.sun.weather_cleared": "The weather has been cleared.", "message.clean.success": "Your inventory has been cleared!", "command.tpahere.request_to": "Teleport request to you from %s:", "message.now_forced": "Player  is now forced into  mode.", "command.vanish.success_for_player": "Vanish activated for player %s.", "command.rain.success": "The weather has been set to rain.", "command.cleanh.no_permission": "You don't have permission to execute this command.", "command.hunger.self.set": "Your hunger level has been set to half.", "command.delwarp.list_empty": "The warp list is empty.", "item.card_6.name": "Card Purple", "tile.bigbox.name": "Big Box", "command.trash.player_not_found": "Player not found.", "command.delwarp.not_found": "Warp '%s' not found.", "tile.kmc.name": "KMC", "message.nodrop.cannotdrop": "You cannot drop items right now.", "command.hudc.toggle.visible": "HUD visibility: visible", "item.fuhrerschein_used.name": "<PERSON>�<PERSON><PERSON><PERSON><PERSON>", "command.fly.granted": "You have been granted flight mode by %s!", "command.home.set.success": "Home '%s' has been set!", "item.card_4.name": "Card Light Gray", "tile.barstool.name": "Bar Stool", "tile.plantvase_4.name": "Plant vase", "message.nodrop.disabled": "NoDrop mode has been disabled.", "tile.tattoo_chair_white.name": "Tattoo Chair", "command.sun.no_arguments": "This command does not support arguments.", "command.fly.enabled": "Fly mode enabled!", "subtitles.doorscan": "", "command.setlore.error_no_item": "Hold an item in your hand to update the lore!", "tile.smooth_stone.name": "Smooth Stone", "command.dynamxkillprops.success": "Removed %d entities with name '%s'", "tile.for_sale_sign.name": "For Sale Sign", "command.tpoffline.usage": "/tpoffline <playername>", "command.vanish.now_visible": "You are now visible again!", "item.chocolate_donut.name": "Chocolate Donut", "command.tpaall.request_message": "Teleportation request to you from %s: ", "command.replacemode.invalid_number": "Invalid number: %s", "command.ec.usage": "/ec [<playername>]", "command.clear.target.success": "The inventory of %s has been cleared by %s!", "command.inv.load.not_found": "No inventory found with name %s.", "item.dienstausweiss_used.name": "Dienstausweis", "command.tpaaccept.invalid_request": "No valid Tpa was sent.", "tile.minewache_logo.name": "Minewache Logo", "command.tpahere.player_not_found": "Player not found or not online.", "item.samsung.name": "Samsung", "command.warprq.accept_hover": "Click here to accept the request.", "item.snow_remover.name": "Snow Remover", "command.heal.all": "All players have been healed.", "item.fahrzeugschein.name": "Fahrzeugschein", "command.hunger.not_found": "Player %s not found.", "command.registerid.profile_error": "Error retrieving player profile.", "command.inv.delete.error": "Failed to delete inventory: %s", "tile.car_spawner.name": "Vehicle Spawner", "message.no_longer_forced": "Player  is no longer forced into any game mode.", "command.inv.load.success": "Inventory loaded from %s.", "command.gm.player_not_found": "Player not found.", "tile.full_path_block.name": "Full Path Block", "command.rename.usage": "/rename <name>", "command.sudo.usage": "/sudo <playername> <command>", "command.trash.invalid_args": "Invalid number of arguments.", "item.baton.name": "Baton", "tile.behandeltes_holz_treppe.name": "Behandeltes Holz Stufe", "command.tpall.success": "All players have been teleported to you.", "tile.lamp.name": "<PERSON><PERSON>", "command.unbreakable.not_a_player": "You must be a player to execute this command without specifying a player.", "tile.pin_board.name": "Pin Board", "subtitles.no_route": "", "command.unbanip.error": "Error while unbanning IP address %s.", "item.coffee_cup.name": "Coffee", "command.fly.executor": "You have granted flight mode to %s!", "tile.card.name": "Card", "command.tpa.self": "You can't send a tpahere to yourself.", "tile.mine_rettung_logo.name": "Minerettung Logo", "tile.whitestoreshelf.name": "White store shelf", "tile.plantvase_1.name": "Plant vase", "command.tphere.usage": "Usage: /tphere <playername>", "command.tempban.player_not_found": "Player not found", "command.tpa.already_requested": "Player already has a request from someone else.", "tile.atm_3.name": "ATM", "item.mwworldcopytool.name": "Mwworldcopytool", "message.banip.usage": "Usage: /banip <Player>", "tile.kitchensink.name": "Kitchen sink", "command.setlore.usage": "/setlore [Old_Lore] [New_Lore]", "tile.openbox.name": "Open box", "command.vanish.reset_for_player": "Vanish reset for player %s.", "command.heal.usage": "Invalid number of arguments. Use /heal [<playername>].", "command.tpahere.accept": "[ACCEPT]", "command.fly.player_not_found": "Player %s not found.", "command.replacemode.not_player": "This command can only be used by a player.", "command.warprq.request_message": "%s wants to warp you to %s:", "tile.sign_test.name": "Sign Test", "command.unbanip.ip_not_found": "IP address or player not found: %s", "command.vehiclepreset.usage": "Usage: /vehiclepreset <vehicleName> <licensePlate>", "command.tpaall.reject_hover": "Click here to reject the request.", "tile.job_block.name": "Job Block", "command.spawn.invalid_args": "Invalid arguments. Usage: /spawn", "tile.sidewalkred.name": "Sidewalk Red", "tile.crate.name": "Crate", "tile.woodenchair.name": "Wooden chair", "command.tpall.only_players": "This command can only be executed by a player.", "command.tpoffline.invalid_pos_data": "Invalid position data for player: %s", "item.ausweis.name": "Ausweis", "command.setview.success": "View direction set successfully.", "item.kartenleser_item.name": "<PERSON><PERSON><PERSON><PERSON>", "tile.palletslab.name": "Pallet slab", "item.klappe.name": "<PERSON><PERSON><PERSON>", "command.money.balance": "Your account balance is: %s", "command.ec.open_ender_chest_self": "You have opened your Ender<PERSON>t", "tile.militarycrate.name": "Military crate", "tile.obstacle.name": "Obstacle", "command.gm.adventure": "Player set to adventure mode: %s.", "key.mcreator.fly_down": "Fly Down", "item.euro_50.name": "F�nfzig Euro", "command.tpahere.usage": "Usage: /tpahere <playername>", "command.vanish.usage": "/vanish [<arguments>]", "key.mcreator.interact": "Interact", "command.replacemodeundo.success": "The last replacement has been undone.", "tile.smallboxes.name": "Small boxes", "command.god.enabled": "God mode enabled!", "command.removelore.not_player": "Only players can execute this command.", "command.fly.revoked": "Your flight mode has been revoked by %s!", "command.tpadeny.request_denied_receiver": "Your teleportation request was denied by %s.", "command.warp.usage": "Usage: /warp <warpname> [<playername>]", "command.unbreakable.success": "The item in the hand of %s is now unbreakable.", "command.home.not_found": "No home with the name '%s' found.", "command.setlore.success": "The lore has been successfully updated!", "command.tpaaccept.accepted_target": "Teleportation request to %s accepted.", "command.tpahere.accept_hover": "Click here to accept the request.", "command.registerid.syntax_error": "Command syntax: /registerid <First Name> <Last Name> <Birth Date> <Height> <Gender>", "command.inv.load.error": "Failed to load inventory: %s", "tile.gundisplay.name": "Weapon Cabinet", "command.tpoffline.success": "Teleported to %s", "command.setwarp.set": "Warp '%s' successfully set.", "command.day.usage": "The command does not support any arguments.", "command.warps.list_title": "Warps:", "tile.atm.name": "ATM", "command.dynamxkillprops.usage": "Invalid arguments. Usage: /dynamxkillprops <name>", "command.tphere.success": "Player %s has been teleported to your location.", "item.vrs_remover.name": "VRS Remover", "command.thunder.usage": "/thunder", "command.removelore.no_item": "Hold an item in your hand to remove lore!", "item.handschellen.name": "Handschellen", "command.thunder.no_arguments": "The command does not support arguments.", "command.delete.success": "Killed %d entities of type %s.", "command.warprq.accept_success": "You have accepted the warp request from %s.", "command.inv.usage": "/inv <save|load|delete> <name>", "command.thunder.success": "The weather has been changed to thunderstorm.", "message.back.success": "You have been teleported to your last position.", "command.glow.only_players": "Only players can use this command!", "command.hudc.toggle.hidden": "HUD visibility: hidden", "tile.garbagebin.name": "Garbage Bin", "command.tpadeny.no_request": "You have not received a teleportation request from someone.", "tile.cardreader.name": "Cardreader", "command.warprq.usage": "Usage: /warprq <warpname> <playername>", "message.addlore.success": "The lore has been added to the item!", "command.registerid.usage": "/registerid <First Name> <Last Name> <Birth Date> <Height> <Sex>", "message.player_not_found": "Player not found: ", "tile.woodbox.name": "Wood box", "command.warp.no_permission": "You need O<PERSON> to teleport other players!", "tile.vendingmachine.name": "Vending machine", "tile.crimson_trapdoor.name": "Crimson Trapdoor", "tile.dark_oak_trapdoor.name": "Dark Oak Trapdoor", "command.tpadeny.sender_not_found": "The Player was not found or is offline.", "command.registerid.success": "You have successfully registered your ID!", "command.glow.success": "The item now glows!", "command.warprq.reject": "REJECT", "item.iphone.name": "Iphone", "command.home.delete.success": "Home '%s' has been deleted!", "item.fuhrerschein.name": "<PERSON>�<PERSON><PERSON><PERSON><PERSON>", "command.tempban.banned_player": "Player {0} has been temporarily banned for {1}", "command.ec.invalid_arguments": "Invalid arguments. Usage: ", "command.god.disabled": "God mode disabled!", "command.warp.error_reading": "Error reading warp information.", "command.feed.self": "Your hunger has been filled.", "command.setview.invalid_args": "Invalid arguments. Pitch and yaw must be numbers.", "tile.storeshelfvariant.name": "Store shelf", "tile.filingcabinet.name": "Filing Cabinet", "command.tempban.invalid_time_format": "Invalid time format", "item.euro_2.name": "Zwei Euro", "command.tpoffline.player_not_found": "Player not found: %s", "command.cleanh.success": "Hotbar cleared successfully!", "command.deleteitems.success": "All items deleted! Number of items deleted: %d", "item.euro_10.name": "Zehn Euro", "command.unbreakable.player_not_found": "Player not found.", "command.tpadeny.request_denied_sender": "You have denied the request from %s.", "command.rename.no_item": "You are not holding an item!", "command.hunger.others.error": "The hunger level of %s cannot be decreased further.", "command.invsee.usage_error": "Usage: /invsee <playername>", "tile.atm_2.name": "ATM", "command.warp.error_format": "Invalid format of warp information.", "command.glow.no_item": "You must hold an item in your main hand!", "command.day.success": "The time of day has been set to day.", "item.card_3.name": "<PERSON>", "command.tpa.accept": "[ACCEPT]", "tile.medicalcrate.name": "Medical crate", "command.head.other": "You have received the head of %s!", "command.unbanip.ip_not_banned": "IP address %s is not banned.", "command.setwarp.usage": "Usage: /setwarp <warpname> [<x> <y> <z> <pitch> <yaw>]", "command.tpaall.reject_button": "[REJECT]", "itemGroup.tabminewache_mod_money": "Minewache Mod Money", "message.banip.notfound": "Player not found", "tile.iron_fence.name": "Iron Fence", "command.rename.success": "Item successfully renamed to %s", "item.euro_500.name": "F�nfhundert Euro", "item.euro_100.name": "Hundert Euro", "message.clear.success.target": "Your inventory has been cleared by %s!", "command.vehiclepreset.spawn_failure": "Failed to spawn vehicle.", "message.to_mode": " mode.", "command.warps.usage": "Usage: /warps [<arguments>]", "command.warprq.reject_hover": "Click here to reject the request.", "command.tpa.deny": "[DENY]", "command.tempban.usage": "/tempban <player> <time>", "command.hunger.others.set": "Your hunger level has been set to half by %s.", "command.gm.survival": "Player set to survival mode: %s.", "command.tpa.request": "Teleport request from %s:", "command.inv.invalid_action": "Invalid action: %s", "command.warprq.reject_success": "You have rejected the warp request from %s.", "command.timespeed.invalid_arguments": "Invalid arguments. Usage: /timespeed <time>", "command.setview.player_only": "This command can only be executed by a player.", "command.setview.usage": "Usage: /setview <pitch> <yaw>", "tile.officechair.name": "Office chair", "command.tpahere.reject_hover": "Click here to reject the request.", "command.rename.not_player": "Only players can execute this command.", "command.tpaaccept.accepted_sender": "Teleportation request accepted by %s", "message.invalid_gamemode": "Invalid gamemode: ", "command.ec.player_not_found": "Player %s not found!", "tile.acacia_trapdoor.name": "Acacia T<PERSON>door", "command.warprq.accept": "ACCEPT", "command.feed.player_only": "This command can only be executed by a player.", "message.nodrop.enabled": "NoDrop mode has been enabled.", "command.inv.delete.success": "Inventory deleted with name %s.", "tile.behandeltes_holz_stufe.name": "Behandeltes Holz Treppe", "tile.storeshelf.name": "Store shelf", "command.dynamxkillvehicles.removed_entities": "Removed ", "tile.plantvase_5.name": "Plant vase", "item.euro_20.name": "Zwanzig Euro", "command.sun.usage": "/sun", "command.inv.usage_error": "Invalid arguments. Usage: /inv <save|load|delete> <name>", "item.ausweisw.name": "Ausweis", "command.money.mysql_not_connected": "MySQL is not connected, command functionality disabled.", "tile.new_sand.name": "New Sand", "command.home.usage": "Usage: /home <name> or /home set|delete.", "tile.table.name": "Table", "item.card_5.name": "Card Pink", "command.gm.creative": "Player set to creative mode: %s.", "item.keyboard_1.name": "Keyboard", "command.delwarp.deleted": "Warp '%s' successfully deleted.", "command.timespeed.usage": "/timespeed <time (e.g., 1m, 1s, 1h)>", "command.tphere.error": "Player not found: %s", "itemGroup.tabmw_building_blocks": "MW Building Blocks", "command.heal.not_found": "Player %s not found.", "command.spawn.file_not_found": "Spawn data file not found.", "command.home.delete.not_found": "No home with the name '%s' found.", "command.tpa.not_found": "Player not found or not online.", "tile.grass_stone_path.name": "Grass Stone Path", "message.forced_mode": "Your game mode is forced to ", "command.tpahere.request_pending": "Player already has a request by someone.", "command.tpadeny.usage": "Usage: /tpadeny", "command.delwarp.delete_error": "Error deleting warp.", "tile.jungle_trapdoor.name": "Jungle Trapdoor", "message.your_mode": "Your game mode is now forced to ", "command.god.invalid_mode": "This command can only be used in Survival or Adventure mode.", "command.gm.invalid_argument": "Invalid argument. Please use /gm 0, /gm 1, /gm 2 or /gm 3.", "tile.whitewoodenchair.name": "White wooden chair", "command.hunger.usage_error": "Invalid number of arguments. Use /hunger [<playername>].", "command.registerid.already_registered": "You are already registered!", "command.timespeed.success": "Time speed set: Full cycle will pass in %s", "command.setwarp.updated": "Warp '%s' successfully updated.", "command.spawn.usage": "Usage: /spawn", "tile.telefon.name": "Telefon", "command.unbreakable.usage": "/unbreakable [<player>]", "command.registerid.processing_error": "Error processing command. Please check your input.", "command.tpaall.sent_confirmation": "Teleportation request sent to all players.", "command.gm.spectator": "Player set to spectator mode: %s.", "command.dynamxkillprops.available_names": "Available names: %s", "tile.exit_sign.name": "Exit Sign", "itemGroup.tabsaros_new_blocks_mod": "Minewache Mod", "tile.fingerprintscanner.name": "Fingerprintscanner", "command.tpahere.reject": "[REJECT]", "command.heal.target": "You have healed %s.", "command.sudo.player_not_found": "Player %s not found."}, "de_de": {"command.tpall.teleported": "Du wurdest zu %s teleportiert.", "command.invsee.player_not_found": "Spieler nicht gefunden: %s", "tile.tanksaule.name": "Zapfs�ule", "command.night.success": "Die Tageszeit wurde auf Nacht gesetzt.", "command.warp.player_not_found": "Spieler %s wurde nicht gefunden.", "command.rain.argument_error": "Dieser Befehl unterst�tzt keine Argumente.", "command.warp.teleport_other_success": "Du hast %s zum Warp: %s teleportiert.", "message.addlore.noitem": "Halte einen Gegenstand in der Hand, um Lore hinzuzuf�gen!", "key.mcreator.hands_up": "Hands Up", "command.setlore.error_old_lore_not_found": "Die alte Lore wurde nicht gefunden!", "command.inv.save.success": "Inventar als %s gespeichert.", "item.euro_5.name": "Zwei Euro", "command.tpaall.usage": "Verwendung: /tpaall", "command.vehiclepreset.invalid_arguments": "Ung�ltige Argumente! Verwendung: /vehiclepreset <vehicleName> <licensePlate>", "command.replacemode.success": "Replacemode auf %d gesetzt", "command.delwarp.usage": "Verwendung: /delwarp <warpname>", "tile.boxshelf.name": "Kistenregal", "command.hunger.all.success": "Die Hungerleisten aller Spieler wurden auf die H�lfte gesetzt.", "command.rain.usage": "/rain", "item.road_updater.name": "Road Updater", "command.heal.by": "<PERSON> wurdest von %s geheilt.", "tile.full_grass_block.name": "Vollst�ndiger Gras-Block", "command.inv.save.error": "Fehler beim Speichern des Inventars: %s", "command.warprq.broadcast_sent": "Warp-Anfrage an alle Spieler au�er dir gesendet.", "command.warps.error_reading": "Fehler beim Lesen der Warp-Informationen.", "command.feed.success_target": "Spieler %s gef�ttert", "item.radio.name": "Radio", "tile.woodwallshelves.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tile.new_gravel.name": "<PERSON><PERSON><PERSON>", "item.fahrzeugschein_used.name": "Dienstausweis", "tile.behandeltes_holz.name": "Behandeltes Holz", "command.delete.usage": "Verwendung: /delete <entityname>", "item.dienstausweiss.name": "<PERSON>�<PERSON><PERSON><PERSON><PERSON>", "command.tpahere.cant_send_to_self": "Du kannst dir selbst keine TPA-Anfrage senden.", "command.feed.player_not_found": "Spieler %s nicht gefunden.", "tile.warped_trapdoor.name": "Warped Trapdoor", "command.registerid.mysql_not_connected": "MySQL ist nicht verbunden, Befehlsfunktion deaktiviert.", "command.setspawn.success": "Spawn wurde gesetzt bei X: %d, Y: %d, Z: %d mit Yaw: %.2f und Pitch: %.2f", "tile.kaffee_maschiene.name": "<PERSON><PERSON><PERSON> Ma<PERSON>e", "item.euro_1.name": "Euro 1", "command.registerid.register_error": "Fehler beim Registrieren.", "item.donut.name": "Donut", "command.trash.usage": "/trash [<Spielername>]", "command.dynamxkillvehicles.invalid_arguments": "Ung�ltige Argumente. Nutzung: ", "tile.plantvase_3.name": "Pflanzenvase", "command.tpaall.accept_button": "[ANNEHMEN] ", "command.tempban.no_arguments": "Verwendung: /tempban <Spieler> <Zeit>", "tile.sidewalk.name": "Gehweg", "command.clear.player.notfound": "Spieler %s nicht gefunden.", "tile.birch_trapdoor.name": "<PERSON>", "command.replacemode.usage": "/replacemode <number>", "command.hunger.usage": "/hunger [<playername>]", "tile.box.name": "<PERSON><PERSON>", "command.replacemodeundo.no_replacements": "<PERSON>ine Ersetzungen zum R�ckg�ngig machen vorhanden.", "command.heal.self": "<PERSON> wurdest geheilt.", "tile.grass_stone_path_2.name": "Gras Steinweg", "command.vehiclepreset.spawn_success": "Fahrzeug gespawnt: %s mit Kennzeichen: %s", "command.home.teleport.success": "Zu Home '%s' teleportiert.", "command.removelore.not_found": "Die angegebene Lore wurde nicht gefunden!", "command.hunger.self.error": "<PERSON><PERSON> kann nicht weiter verringert werden.", "command.inv.delete.not_found": "<PERSON>in Inventar mit dem Namen %s gefunden.", "command.night.argument_error": "Dieser Befehl unterst�tzt keine Argumente.", "command.hunger.single.success": "Du hast die Hungerleiste von %s auf die H�lfte gesetzt.", "subtitles.radio2": "", "item.dokumente.name": "Dokumente", "item.coffee.name": "<PERSON><PERSON><PERSON>", "message.broadcast.usage": "Verwendung: /broadcast <Nachricht>", "command.warp.not_found": "Warp '%s' wurde nicht gefunden.", "command.tpa.usage": "/tpa [<Spielername>]", "message.you_can_change": "Sie k�nnen jetzt wieder Ihren Spielmodus �ndern.", "command.tpoffline.no_data": "<PERSON>ine gespeicherten Daten f�r Spieler: %s", "tile.barrel.name": "Fass", "item.card_2.name": "Card Black", "tile.plantvase_2.name": "Pflanzenvase", "tile.kitchencabinet.name": "<PERSON>�<PERSON>", "command.night.usage": "/night", "command.inv.save.exists": "Dieses Inv Exsistiert Bereits", "item.euro_200.name": "F�nf Euro", "item.ausweis_used.name": "Ausweis", "command.setwarp.error": "<PERSON><PERSON> beim Setzen des Warps.", "item.taser.name": "Taser", "item.replace_tool.name": "Replace Tool", "command.tpall.usage": "Benutzung: /tpall", "command.home.set.exists": "Ein Home mit dem Namen '%s' existiert bereits.", "message.usage": "Verwendung: /gmforcev <playername> <gamemode>", "message.clean.usage": "Der Befehl unterst�tzt keine Argumente.", "tile.kartenleser.name": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.klappe": "Und Aktion", "command.tpall.no_permission": "Du hast nicht die erforderliche Berechtigung f�r diesen Befehl.", "command.warprq.reject_target": "Die Anfrage von %s wurde abgelehnt.", "message.back.noposition": "<PERSON>ine letzte Position gefunden.", "command.tpaall.accept_hover": "<PERSON><PERSON><PERSON> hier, um die Anfrage anzunehmen.", "tile.smallboxshelf.name": "<PERSON><PERSON>", "command.home.set.usage": "Verwendung: /home set <name>", "command.unbreakable.no_item": "Der Spieler %s hat kein Item in der Hand.", "command.gm.invalid_mode": "Ung�ltiger Spielmodus. Bitte benutze /gm 0, /gm 1, /gm 2 oder /gm 3.", "tile.spotlight.name": "Scheinwerfer", "command.invsee.not_player": "<PERSON>ur Spieler k�nnen diesen Befehl verwenden.", "command.dynamxkillvehicles.available_names": "Verf�gbare Namen: ", "command.gm.no_arguments": "Keine Argumente angegeben. Bitte benutze /gm 0, /gm 1, /gm 2 oder /gm 3.", "command.invsee.usage": "/invsee <spielername>", "command.spawn.teleport_success": "Du wurdest zum Spawn teleportiert.", "subtitles.radio": "", "subtitles.cuffsound": "", "command.ec.open_ender_chest": "Endertruhe von %s ge�ffnet", "tile.tattoo_chair.name": "Tattoo Chair", "command.head.self": "Du hast deinen eigenen Kopf erhalten!", "command.unbreakable.standard": "Das Item ist jetzt unzerst�rbar", "message.banip.kicked": "Du wurdest permanent gebannt", "command.dynamxkillvehicles.with_name": " Entit�ten mit Namen '", "tile.garage.name": "Garage", "item.card_1.name": "Card Black", "command.timespeed.invalid_time_format": "Ung�ltiges Zeitformat. Verwende <Zahl><s|m|h>, z.B. 1m f�r 1 Minute.", "tile.iron_fence_edge.name": "Iron Fence Edge", "command.removelore.success": "Die Lore wurde erfolgreich entfernt!", "command.hunger.single.error": "Die Hungerleiste von %s kann nicht weiter verringert werden.", "tile.bench.name": "Tisch", "item.die_minewache.name": "Die Minewache", "command.unbanip.usage": "/unbanip <IP-Adresse>", "command.setwarp.exists": "Der Warp '%s' existiert bereits.", "command.tpaaccept.usage": "Verwendung: /tpaaccept", "command.warprq.request_sent": "Warp-Anfrage an %s gesendet.", "command.tpaaccept.not_found": "Der Spieler wurde nicht gefunden oder ist offline.", "message.back.playeronly": "<PERSON>ser Befehl kann nur von Spielern verwendet werden.", "tile.kasse.name": "<PERSON><PERSON>", "command.tpaaccept.no_request": "Du hast keine Teleportationsanfrage von jemandem erhalten.", "tile.spruce_trapdoor.name": "Spruce Trapdoor", "item.taser_ammo.name": "Taser Ammo", "command.feed.usage": "Ung�ltige Befehlsnutzung. Verwende /feed [spieler]", "command.warp.teleport_success": "Du wurdest zum Warp: %s teleportiert.", "command.vanish.now_invisible": "Du bist nun unsichtbar!", "tile.pallet.name": "<PERSON><PERSON><PERSON>", "command.fly.disabled": "Flugmodus deaktiviert!", "command.hunger.others.success": "Du hast die Hungerleiste von %s auf die H�lfte gesetzt.", "command.clear.self.success": "Dein <PERSON>ar wurde geleert!", "command.unbanip.success": "IP-Adresse %s wurde entbannt.", "command.sudo.force_command": "Erzwungen %s den Befehl auszuf�hren: %s", "message.addlore.duplicate": "Du darfst nicht 2 <PERSON><PERSON> mit dem gleichen Namen haben!", "command.home.delete.usage": "Verwendung: /home delete <name>", "command.removelore.usage": "/removelore [Lore]", "command.sun.weather_cleared": "<PERSON> Wetter wurde gecleart.", "message.clean.success": "Dein <PERSON>ar wurde geleert!", "command.tpahere.request_to": "Teleportationsanfrage zu dir von %s:", "message.now_forced": "<PERSON>pieler  ist jetzt gezwungen, im  Modus zu spielen.", "command.vanish.success_for_player": "Vanish aktiviert f�r Spieler %s.", "command.rain.success": "<PERSON> Wetter wurde auf Regen ge�ndert.", "command.cleanh.no_permission": "Du hast keine Berechtigung, diesen Befehl auszuf�hren.", "command.hunger.self.set": "<PERSON><PERSON> wurde auf die H�lfte gesetzt.", "command.delwarp.list_empty": "Die Warpliste ist leer.", "item.card_6.name": "Card Pink", "tile.bigbox.name": "<PERSON><PERSON>�<PERSON>", "command.trash.player_not_found": "Spieler nicht gefunden.", "command.delwarp.not_found": "Warp '%s' wurde nicht gefunden.", "tile.kmc.name": "KMC", "message.nodrop.cannotdrop": "Du kannst momentan keine Gegenst�nde fallen lassen.", "command.hudc.toggle.visible": "HUD-Sichtbarkeit: sichtbar", "item.fuhrerschein_used.name": "<PERSON>�<PERSON><PERSON><PERSON><PERSON>", "command.fly.granted": "Du hast Flugmodus von %s erhalten!", "command.home.set.success": "Home '%s' wurde gesetzt!", "item.card_4.name": "<PERSON>", "tile.barstool.name": "<PERSON><PERSON><PERSON>", "tile.plantvase_4.name": "Pflanzenvase", "message.nodrop.disabled": "NoDrop-Modus wurde deaktiviert.", "tile.tattoo_chair_white.name": "Tattoo Chair", "command.sun.no_arguments": "Der Befehl unterst�tzt keine Argumente.", "command.fly.enabled": "Flugmodus aktiviert!", "subtitles.doorscan": "", "command.setlore.error_no_item": "Halte einen Gegenstand in der Hand, um die Lore zu aktualisieren!", "tile.smooth_stone.name": "<PERSON><PERSON>", "command.dynamxkillprops.success": "Entfernt %d Entit�ten mit Namen '%s'", "tile.for_sale_sign.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command.tpoffline.usage": "/tpoffline <Spielername>", "command.vanish.now_visible": "Du bist wieder sichtbar!", "item.chocolate_donut.name": "Schokoladen Donut", "command.tpaall.request_message": "Teleportationsanfrage zu sich von %s: ", "command.replacemode.invalid_number": "Ung�ltige Zahl: %s", "command.ec.usage": "/ec [<spielername>]", "command.clear.target.success": "Inventar von %s wurde von %s geleert!", "command.inv.load.not_found": "<PERSON>in Inventar mit dem Namen %s gefunden.", "item.dienstausweiss_used.name": "<PERSON>�<PERSON><PERSON><PERSON><PERSON>", "command.tpaaccept.invalid_request": "<PERSON>s wurde keine g�ltige Tpa gesendet.", "tile.minewache_logo.name": "Minewache Logo", "command.tpahere.player_not_found": "Spieler nicht gefunden oder nicht online.", "item.samsung.name": "Samsung", "command.warprq.accept_hover": "<PERSON><PERSON><PERSON> hier, um die Anfrage anzunehmen.", "item.snow_remover.name": "Snow Remover", "command.heal.all": "<PERSON>e Spieler wurden geheilt.", "item.fahrzeugschein.name": "Dienstausweis", "command.hunger.not_found": "Spieler %s nicht gefunden.", "command.registerid.profile_error": "Fehler beim Abrufen des Spielerprofils.", "command.inv.delete.error": "Fehler beim L�schen des Inventars: %s", "tile.car_spawner.name": "Vehicle Spawner", "message.no_longer_forced": "<PERSON>pieler  ist nicht mehr gezwungen, einen Spielmodus zu verwenden.", "command.inv.load.success": "Inventar von %s geladen.", "command.gm.player_not_found": "Spieler nicht gefunden.", "tile.full_path_block.name": "Vollst�ndiger Pfad-Block", "command.rename.usage": "/rename <name>", "command.sudo.usage": "/sudo <playername> <command>", "command.trash.invalid_args": "Ung�ltige Anzahl an Argumenten.", "item.baton.name": "Baton", "tile.behandeltes_holz_treppe.name": "Behandel<PERSON>", "command.tpall.success": "Alle Spieler wurden zu dir teleportiert.", "tile.lamp.name": "Lam<PERSON>", "command.unbreakable.not_a_player": "Du musst ein S<PERSON>ler sein, um diesen Befehl ohne Spielerangabe auszuf�hren.", "tile.pin_board.name": "Pin Board", "subtitles.no_route": "", "command.unbanip.error": "Fehler beim Entbannen der IP-Adresse %s.", "item.coffee_cup.name": "<PERSON><PERSON><PERSON>", "command.fly.executor": "Du hast Flugmodus an %s gegeben!", "tile.card.name": "Card", "command.tpa.self": "Du kannst dir selbst keine tpahere-Anfrage senden.", "tile.mine_rettung_logo.name": "Minewache Logo", "tile.whitestoreshelf.name": "Lagerregal <PERSON>�", "tile.plantvase_1.name": "Pflanzenvase", "command.tphere.usage": "Verwendung: /tphere <Spielername>", "command.tempban.player_not_found": "Spieler nicht gefunden", "command.tpa.already_requested": "Spieler hat bereits eine Anfrage von j<PERSON>d and<PERSON>.", "tile.atm_3.name": "ATM", "item.mwworldcopytool.name": "Mwworldcopytool", "message.banip.usage": "Verwendung: /banip <Spieler>", "tile.kitchensink.name": "<PERSON>�<PERSON>", "command.setlore.usage": "/setlore [Old_Lore] [New_Lore]", "tile.openbox.name": "<PERSON>�<PERSON> (office chair)", "command.vanish.reset_for_player": "Vanish zur�ckgesetzt f�r Spieler %s.", "command.heal.usage": "Ung�ltige Anzahl von Argumenten. Verwende /heal [<Spielername>].", "command.tpahere.accept": "[ANNEHMEN]", "command.fly.player_not_found": "Spieler %s nicht gefunden.", "command.replacemode.not_player": "<PERSON>ser Befehl kann nur von einem Spieler ausgef�hrt werden.", "command.warprq.request_message": "%s m�chte dich nach %s warpen:", "tile.sign_test.name": "Sign Test", "command.unbanip.ip_not_found": "IP-Adresse oder Spieler nicht gefunden: %s", "command.vehiclepreset.usage": "Verwendung: /vehiclepreset <vehicleName> <licensePlate>", "command.tpaall.reject_hover": "<PERSON><PERSON><PERSON> hier, um die Anfrage abzulehnen.", "tile.job_block.name": "Job Block", "command.spawn.invalid_args": "Ung�ltige Argumente. Verwendung: /spawn", "tile.sidewalkred.name": "Gehweg Rot", "tile.crate.name": "Kistenregal (Box shelf) ", "tile.woodenchair.name": "<PERSON><PERSON><PERSON>", "command.tpall.only_players": "<PERSON>ser Befehl kann nur von einem Spieler ausgef�hrt werden.", "command.tpoffline.invalid_pos_data": "Ung�ltige Positionsdaten f�r Spieler: %s", "item.ausweis.name": "Ausweis", "command.setview.success": "Blickrichtung erfolgreich gesetzt.", "item.kartenleser_item.name": "<PERSON><PERSON><PERSON><PERSON>", "tile.palletslab.name": "<PERSON><PERSON><PERSON>", "item.klappe.name": "<PERSON><PERSON><PERSON>", "command.money.balance": "<PERSON><PERSON> betr�gt: %s", "command.ec.open_ender_chest_self": "Du hast deine Enderchest ge�ffnet", "tile.militarycrate.name": "<PERSON><PERSON>�<PERSON>", "tile.obstacle.name": "<PERSON><PERSON><PERSON>", "command.gm.adventure": "Spieler in den Abenteuermodus versetzt: %s.", "key.mcreator.fly_down": "Fly Down", "item.euro_50.name": "F�nf Euro", "command.tpahere.usage": "Verwendung: /tpahere <Spielername>", "command.vanish.usage": "/vanish [<Argumente>]", "key.mcreator.interact": "Interact", "command.replacemodeundo.success": "Die letzte Ersetzung wurde r�ckg�ngig gemacht.", "tile.smallboxes.name": "<PERSON><PERSON>", "command.god.enabled": "God-<PERSON><PERSON> aktiviert!", "command.fly.revoked": "Dein F<PERSON> wurde von %s entzogen!", "command.removelore.not_player": "Nur Spieler k�nnen diesen Befehl ausf�hren.", "command.tpadeny.request_denied_receiver": "<PERSON>ine Teleportationsanfrage wurde von %s abgelehnt.", "command.warp.usage": "Verwendung: /warp <warpname> [<playername>]", "command.unbreakable.success": "Das Item in der Hand von %s ist jetzt unzerst�rbar.", "command.home.not_found": "Kein Home mit dem Namen '%s' gefunden.", "command.setlore.success": "Die Lore wurde erfolgreich aktualisiert!", "command.tpaaccept.accepted_target": "Teleportationsanfrage an %s akzeptiert.", "command.tpahere.accept_hover": "<PERSON><PERSON><PERSON> hier, um die Anfrage anzunehmen.", "command.registerid.syntax_error": "Befehlssyntax: /registerid <Vorname> <Nachname> <Geburtsdatum> <Gr��e> <Geschlecht>", "command.inv.load.error": "Fehler beim Laden des Inventars: %s", "tile.gundisplay.name": "Waffensch<PERSON>", "command.tpoffline.success": "Teleportiert zu %s", "command.setwarp.set": "Warp '%s' erfolgreich gesetzt.", "command.day.usage": "Der Befehl unterst�tzt keine Argumente.", "command.warps.list_title": "Warps:", "tile.atm.name": "ATM", "command.dynamxkillprops.usage": "Ung�ltige Argumente. Nutzung: /dynamxkillprops <name>", "command.tphere.success": "Spieler %s wurde zu deiner Position teleportiert.", "item.vrs_remover.name": "VRS Remover", "command.thunder.usage": "/thunder", "command.removelore.no_item": "Halte einen Gegenstand in der Hand, um die Lore zu entfernen!", "item.handschellen.name": "Handschellen", "command.thunder.no_arguments": "Der Befehl unterst�tzt keine Argumente.", "command.delete.success": "<PERSON>s wurden %d Entit�ten des Typs %s get�tet.", "command.warprq.accept_success": "Du hast die Warp-Anfrage von %s angenommen.", "command.inv.usage": "/inv <save|load|delete> <name>", "command.thunder.success": "<PERSON> Wetter wurde auf Gewitter ge�ndert.", "message.back.success": "Du wurdest zu deiner letzten Position teleportiert.", "command.glow.only_players": "<PERSON>ur Spieler k�nnen diesen Befehl verwenden!", "command.hudc.toggle.hidden": "HUD-Sichtbarkeit: versteckt", "tile.garbagebin.name": "<PERSON>�<PERSON><PERSON><PERSON>", "command.tpadeny.no_request": "Du hast keine Teleportationsanfrage von jemandem erhalten.", "tile.cardreader.name": "Cardreader", "command.warprq.usage": "Verwendung: /warprq <warpname> <playername>", "message.addlore.success": "Die Lore wurde dem Gegenstand hinzugef�gt!", "command.registerid.usage": "/registerid <First Name> <Last Name> <Birth Date> <Height> <Sex>", "message.player_not_found": "Spieler nicht gefunden: ", "tile.woodbox.name": "<PERSON><PERSON><PERSON>", "command.warp.no_permission": "Du brauchst OP-<PERSON><PERSON><PERSON>, um andere Spieler zu teleportieren!", "tile.vendingmachine.name": "Verkaufsautomat", "tile.crimson_trapdoor.name": "Crimson Trapdoor", "tile.dark_oak_trapdoor.name": "Dark Oak Trapdoor", "command.tpadeny.sender_not_found": "Der Spieler wurde nicht gefunden oder ist offline.", "command.registerid.success": "Du hast deinen Ausweis erfolgreich registriert!", "command.glow.success": "Das Item leuchtet jetzt!", "command.warprq.reject": "ABLEHNEN", "item.iphone.name": "Iphone", "command.home.delete.success": "Home '%s' wurde gel�scht!", "item.fuhrerschein.name": "Ausweis", "command.tempban.banned_player": "<PERSON><PERSON><PERSON> {0} wurde vor�bergehend f�r {1} geb<PERSON>t", "command.ec.invalid_arguments": "Ung�ltige Argumente. Nutzung: ", "command.god.disabled": "<PERSON>-<PERSON><PERSON>!", "command.warp.error_reading": "Fehler beim Lesen der Warp-Informationen.", "command.feed.self": "<PERSON>in <PERSON> wurde aufgef�llt.", "command.setview.invalid_args": "Ung�ltige Argumente. Pitch und Yaw m�ssen Zahlen sein.", "tile.storeshelfvariant.name": "Lagerregal", "tile.filingcabinet.name": "<PERSON><PERSON>nk", "command.tempban.invalid_time_format": "Ung�ltiges Zeitformat", "item.euro_2.name": "Einen Euro", "command.tpoffline.player_not_found": "Spieler nicht gefunden: %s", "command.cleanh.success": "Hotbar erfolgreich geleert!", "command.deleteitems.success": "Alle Items gel�scht! Anzahl der gel�schten Items: %d", "item.euro_10.name": "F�nf Euro", "command.unbreakable.player_not_found": "Spieler nicht gefunden.", "command.tpadeny.request_denied_sender": "Du hast die Anfrage von %s abgelehnt.", "command.rename.no_item": "Du h�ltst keinen Gegenstand in der Hand!", "command.hunger.others.error": "Die Hungerleiste von %s kann nicht weiter verringert werden.", "command.invsee.usage_error": "Verwendung: /invsee <spielername>", "tile.atm_2.name": "ATM", "command.warp.error_format": "Ung�ltiges Format der Warp-Informationen.", "command.glow.no_item": "Du musst ein Item in deiner Haupt-Hand halten!", "command.day.success": "Die Tageszeit wurde auf Tag gesetzt.", "item.card_3.name": "Card Gold", "command.tpa.accept": "[ANNEHMEN]", "tile.medicalcrate.name": "<PERSON><PERSON><PERSON>", "command.head.other": "Du hast den Kopf von %s erhalten!", "command.unbanip.ip_not_banned": "IP-Adresse %s ist nicht gebannt.", "command.setwarp.usage": "Verwendung: /setwarp <warpname> [<x> <y> <z> <pitch> <yaw>]", "command.tpaall.reject_button": "[ABLEHNEN]", "itemGroup.tabminewache_mod_money": "Minewache Mod Money", "message.banip.notfound": "Spieler nicht gefunden", "tile.iron_fence.name": "Iron Fence", "command.rename.success": "Gegenstand erfolgreich umbenannt zu %s", "item.euro_500.name": "F�nf Euro", "item.euro_100.name": "F�nf Euro", "message.clear.success.target": "Dein In<PERSON>ar wurde von %s geleert!", "command.vehiclepreset.spawn_failure": "Fahrzeug konnte nicht gespawnt werden.", "message.to_mode": " Modus.", "command.warps.usage": "Verwendung: /warps [<arguments>]", "command.warprq.reject_hover": "<PERSON><PERSON><PERSON> hier, um die Anfrage abzulehnen.", "command.tpa.deny": "[ABLEHNEN]", "command.tempban.usage": "/tempban <Spieler> <Zeit>", "command.hunger.others.set": "<PERSON><PERSON> wurde auf die H�lfte gesetzt von %s.", "command.gm.survival": "Spieler in den �berlebensmodus versetzt: %s.", "command.tpa.request": "Teleportationsanfrage von %s:", "command.inv.invalid_action": "Ung�ltige Aktion: %s", "command.warprq.reject_success": "Du hast die Warp-Anfrage von %s abgelehnt.", "command.timespeed.invalid_arguments": "Ung�ltige Argumente. Verwendung: /timespeed <Zeit>", "command.setview.player_only": "<PERSON>ser Befehl kann nur von einem Spieler ausgef�hrt werden.", "command.setview.usage": "Verwendung: /setview <pitch> <yaw>", "tile.officechair.name": "<PERSON>�<PERSON>", "command.tpahere.reject_hover": "<PERSON><PERSON><PERSON> hier, um die Anfrage abzulehnen.", "command.rename.not_player": "Nur Spieler k�nnen diesen Befehl ausf�hren.", "command.tpaaccept.accepted_sender": "Teleportationsanfrage von %s akzeptiert", "message.invalid_gamemode": "Ung�ltiger Spielmodus: ", "command.ec.player_not_found": "Spieler %s nicht gefunden!", "tile.acacia_trapdoor.name": "Acacia T<PERSON>door", "command.warprq.accept": "ANNEHMEN", "command.feed.player_only": "<PERSON>ser Befehl kann nur von einem Spieler ausgef�hrt werden.", "message.nodrop.enabled": "NoDrop-Modus wurde aktiviert.", "command.inv.delete.success": "Inventar mit dem Namen %s gel�scht.", "tile.behandeltes_holz_stufe.name": "Behandelte Holzpstufe", "tile.storeshelf.name": "Lagerregal", "command.dynamxkillvehicles.removed_entities": "Entfernt ", "tile.plantvase_5.name": "Pflanzenvase", "item.euro_20.name": "F�nf Euro", "command.sun.usage": "/sun", "command.inv.usage_error": "Ung�ltige Argumente. Verwendung: /inv <save|load|delete> <name>", "item.ausweisw.name": "Ausweis", "command.money.mysql_not_connected": "MySQL ist nicht verbunden, Befehlsfunktion deaktiviert.", "tile.new_sand.name": "<PERSON><PERSON><PERSON>", "command.home.usage": "Verwendung: /home <name> oder /home set|delete.", "tile.table.name": "Tisch", "item.card_5.name": "Card Light Gray", "command.gm.creative": "Spieler in den Kreativmodus versetzt: %s.", "item.keyboard_1.name": "Keyboard", "command.delwarp.deleted": "Warp '%s' wurde erfolgreich gel�scht.", "command.timespeed.usage": "/timespeed <Zeit (z.B. 1m, 1s, 1h)>", "command.tphere.error": "Spieler nicht gefunden: %s", "itemGroup.tabmw_building_blocks": "MW Building Blocks", "command.heal.not_found": "Spieler %s nicht gefunden.", "command.spawn.file_not_found": "Spawn-Daten-Datei nicht gefunden.", "command.home.delete.not_found": "Kein Home mit dem Namen '%s' gefunden.", "command.tpa.not_found": "Spieler nicht gefunden oder nicht online.", "tile.grass_stone_path.name": "Gras Steinweg", "message.forced_mode": "Ihr Spielmodus ist gezwungen zu ", "command.tpahere.request_pending": "Spieler hat bereits eine Anfrage von jemandem.", "command.tpadeny.usage": "Verwendung: /tpadeny", "command.delwarp.delete_error": "Fehler beim L�schen des Warps.", "tile.jungle_trapdoor.name": "Jungle Trapdoor", "message.your_mode": "Ihr Spielmodus ist jetzt gezwungen, zu ", "command.god.invalid_mode": "Dieser Befehl kann nur im �berlebens- oder Abenteuermodus verwendet werden.", "command.gm.invalid_argument": "Ung�ltiges Argument. Bitte benutze /gm 0, /gm 1, /gm 2 oder /gm 3.", "tile.whitewoodenchair.name": "<PERSON>�<PERSON>", "command.hunger.usage_error": "Ung�ltige Anzahl von Argumenten. Verwende /hunger [<Spielername>].", "command.registerid.already_registered": "Du bist bereits registriert!", "command.timespeed.success": "Zeitgeschwindigkeit gesetzt: Ein voller Zyklus dauert jetzt %s", "command.setwarp.updated": "Warp '%s' erfolgreich aktualisiert.", "command.spawn.usage": "Verwendung: /spawn", "tile.telefon.name": "Telefon", "command.unbreakable.usage": "/unbreakable [<Spieler>]", "command.registerid.processing_error": "Fehler bei der Verarbeitung des Befehls. Bitte �berpr�fen Sie Ihre Eingabe.", "command.tpaall.sent_confirmation": "Teleportationsanfrage an alle Spieler gesendet.", "command.gm.spectator": "Spieler in den Zuschauermodus versetzt: %s.", "command.dynamxkillprops.available_names": "Verf�gbare Namen: %s", "tile.exit_sign.name": "Exit Sign", "itemGroup.tabsaros_new_blocks_mod": "Saros New Blocks Mod", "tile.fingerprintscanner.name": "Fingerprintscanner", "command.tpahere.reject": "[ABLEHNEN]", "command.heal.target": "Du hast %s geheilt.", "command.sudo.player_not_found": "Spieler %s nicht gefunden."}}, "foldersRoot": {"name": "~", "children": [{"name": "PROP system", "children": []}, {"name": "Car_Spawner", "children": []}, {"name": "Navi", "children": []}, {"name": "Essentials", "children": [{"name": "warps", "children": []}, {"name": "tpa-system", "children": []}]}, {"name": "ClickCommands", "children": []}, {"name": "Teams", "children": []}, {"name": "Global", "children": []}, {"name": " <PERSON>ees ModernProps", "children": []}, {"name": "ReplaceTool", "children": []}, {"name": "deliciousdelights", "children": []}, {"name": "Hotkeys", "children": []}, {"name": "SchlafSystem", "children": []}, {"name": "CreativeTabs", "children": []}, {"name": "Network", "children": []}, {"name": "Routes-System", "children": []}, {"name": "Tests", "children": [{"name": "Animations", "children": []}, {"name": "GuiPacketSend", "children": []}, {"name": "BlockRenderTest", "children": []}, {"name": "GUISlotClickTest", "children": []}, {"name": "gctest", "children": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "children": []}]}, {"name": "newblocks", "children": [{"name": "MWBuildingBlocks", "children": []}, {"name": "Fingerprint scanner", "children": []}]}, {"name": "Discord", "children": [{"name": "Discord-<PERSON><PERSON>", "children": [{"name": "MW Discord System", "children": []}]}, {"name": "RPC", "children": []}]}, {"name": "Dateiverwaltung", "children": []}, {"name": "Block-Regen", "children": []}, {"name": "ID-System", "children": []}, {"name": "RP-Stuff", "children": []}, {"name": "worldguard", "children": []}, {"name": "GUI Creator", "children": []}, {"name": "Job-System", "children": []}, {"name": "Security", "children": []}, {"name": "Polizei", "children": []}, {"name": "Economy-System", "children": [{"name": "Money", "children": []}]}, {"name": "MW-World-Copy", "children": []}, {"name": "Haus-System", "children": []}]}, "workspaceSettings": {"modid": "saros_new_blocks_mod", "modName": "Minewache Mod", "version": "4.8 Beta", "description": "An extensive role-playing mod for Minecraft 1.12.2 featuring new functionalities such as MySQL integration, job and vehicle systems, along with numerous commands and items for an enhanced player experience.", "author": "Sa<PERSON><PERSON>ch, Tihi2k8", "websiteURL": "https://mcreator.net", "license": "All Rights Reserved", "disableForgeVersionCheck": true, "serverSideOnly": false, "modPicture": "m<PERSON><PERSON>", "requiredMods": [], "dependencies": [], "dependants": [], "mcreatorDependencies": [], "currentGenerator": "forge-1.12.2", "credits": "Created using mod maker MC<PERSON> - https://mcreator.net/about", "modElementsPackage": "net.mcreator.sarosnewblocksmod", "lockBaseModFiles": true}, "mcreatorVersion": 202100118117}