buildscript {
    repositories {
        maven { url = 'https://maven.minecraftforge.net' }
        mavenCentral()
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:3.+'
    }
}
        
apply plugin: 'net.minecraftforge.gradle'
apply plugin: 'eclipse'

version = "1.0"
group = "com.yourname.modid"
archivesBaseName = "modid"

sourceCompatibility = targetCompatibility = compileJava.sourceCompatibility = compileJava.targetCompatibility = '1.8' // Need this here so eclipse task generates correctly.

minecraft {
    mappings channel: 'snapshot', version: '20171003-1.12'

    runs {
        client {
            workingDirectory project.file('run')

            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'

            mods {
                examplemod {
                    source sourceSets.main
                }
            }
        }

        server {
            workingDirectory project.file('run')

            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'

            mods {
                examplemod {
                    source sourceSets.main
                }
            }
        }
    }
}

sourceSets {
    main { output.resourcesDir = output.classesDir }
}

repositories {
    jcenter()

    	maven {
        url 'https://maven.dynamx.fr/artifactory/DynamXRepo'
    }
    maven {
        url 'https://maven.dynamx.fr/artifactory/ACsGuisRepo'
    }
    maven {
        name = "CurseForge"
        url = "https://minecraft.curseforge.com/api/maven/"
    }
    }

dependencies {
    minecraft 'net.minecraftforge:forge:1.12.2-14.23.5.2855'
    compile "fr.dynamx:DynamX:4.0.1-beta"
    compile "fr.aym.acsguis:ACsGuis:1.2.12"
    compile fileTree(dir: 'libs', include: '*.jar')

        compile 'obfuscate:Obfuscate:0.2.6:1.12.2'




    implementation 'net.dv8tion:JDA:5.0.0-alpha.11'

    compile group: 'mysql', name: 'mysql-connector-java', version: '8.0.23'
}