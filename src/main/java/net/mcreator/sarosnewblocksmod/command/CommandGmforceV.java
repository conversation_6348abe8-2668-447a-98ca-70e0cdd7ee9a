package net.mcreator.sarosnewblocksmod.command;

import net.minecraft.command.ICommand;
import net.minecraft.command.ICommandSender;
import net.minecraft.command.CommandBase;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.translation.I18n;
import net.minecraft.world.GameType;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.event.FMLServerStartingEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.PlayerEvent.PlayerLoggedInEvent;
import net.minecraftforge.fml.common.gameevent.PlayerEvent.PlayerChangedDimensionEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import net.mcreator.sarosnewblocksmod.ElementsSarosNewBlocksModMod;
import net.mcreator.sarosnewblocksmod.Dateiverwaltung;

import net.mcreator.sarosnewblocksmod.network.LangNetworkHandler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ElementsSarosNewBlocksModMod.ModElement.Tag
public class CommandGmforceV extends ElementsSarosNewBlocksModMod.ModElement {
    private static final Map<String, GameType> playerForcedGameModes = new HashMap<>();

    public CommandGmforceV(ElementsSarosNewBlocksModMod instance) {
        super(instance, 380);
        MinecraftForge.EVENT_BUS.register(this);
    }

    @Override
    public void serverLoad(FMLServerStartingEvent event) {
        event.registerServerCommand(new CommandHandler());
    }

    public static class CommandHandler implements ICommand {
        @Override
        public int compareTo(ICommand c) {
            return getName().compareTo(c.getName());
        }

        @Override
        public boolean checkPermission(MinecraftServer server, ICommandSender sender) {
            return sender.canUseCommand(2, getName());
        }

        @Override
        public List<String> getAliases() {
            return new ArrayList<>();
        }

        @Override
        public List<String> getTabCompletions(MinecraftServer server, ICommandSender sender, String[] args, BlockPos pos) {
            if (args.length == 1) {
                return CommandBase.getListOfStringsMatchingLastWord(args, server.getOnlinePlayerNames());
            } else if (args.length == 2) {
                return CommandBase.getListOfStringsMatchingLastWord(args, "survival", "creative", "adventure", "spectator");
            }
            return new ArrayList<>();
        }

        @Override
        public boolean isUsernameIndex(String[] args, int index) {
            return index == 0;
        }

        @Override
        public String getName() {
            return "gmforcev";
        }

        @Override
        public String getUsage(ICommandSender sender) {
            return "/gmforcev <playername> <gamemode>";
        }

        @Override
        public void execute(MinecraftServer server, ICommandSender sender, String[] args) {
            if (args.length < 2) {
                sender.sendMessage(new TextComponentString(Dateiverwaltung.warning + I18n.translateToLocal("message.usage")));
                return;
            }

            String playerName = args[0];
            String gameModeString = args[1];
            GameType gameMode;

            switch (gameModeString.toLowerCase()) {
                case "survival":
                    gameMode = GameType.SURVIVAL;
                    break;
                case "creative":
                    gameMode = GameType.CREATIVE;
                    break;
                case "adventure":
                    gameMode = GameType.ADVENTURE;
                    break;
                case "spectator":
                    gameMode = GameType.SPECTATOR;
                    break;
                default:
                    LangNetworkHandler.sendTranslationMessage(sender, "message.invalid_gamemode", Dateiverwaltung.error, gameModeString);
                    return;
            }

            EntityPlayerMP player = server.getPlayerList().getPlayerByUsername(playerName);

            if (player == null) {
            	LangNetworkHandler.sendTranslationMessage(sender, "message.player_not_found", Dateiverwaltung.error, playerName);
                return;
            }

            if (playerForcedGameModes.containsKey(playerName)) {
                playerForcedGameModes.remove(playerName);
                LangNetworkHandler.sendTranslationMessage(sender, "message.no_longer_forced", Dateiverwaltung.sucess, playerName);
                LangNetworkHandler.sendTranslationMessage(player, "message.you_can_change", Dateiverwaltung.sucess);
                server.getCommandManager().executeCommand(server, "cpm setskin -r " + playerName);
            } else {
                playerForcedGameModes.put(playerName, gameMode);
                LangNetworkHandler.sendTranslationMessage(sender, "message.now_forced", Dateiverwaltung.sucess, playerName, gameMode.getName());
                LangNetworkHandler.sendTranslationMessage(player, "message.your_mode", Dateiverwaltung.sucess, gameMode.getName());
                player.setGameType(gameMode);
                server.getCommandManager().executeCommand(server, "cpm setskin -f " + playerName + " UwsBAQMKAAEBAAgCCQEAAAAAADA=");
            }
        }
    }

    @SubscribeEvent
    public void onPlayerLoggedIn(PlayerLoggedInEvent event) {
        if (event.player instanceof EntityPlayerMP) {
            EntityPlayerMP player = (EntityPlayerMP) event.player;
            enforceGameModeRestriction(player);
        }
    }

    @SubscribeEvent
    public void onPlayerChangedDimension(PlayerChangedDimensionEvent event) {
        if (event.player instanceof EntityPlayerMP) {
            EntityPlayerMP player = (EntityPlayerMP) event.player;
            enforceGameModeRestriction(player);
        }
    }

    @SubscribeEvent
    public void onPlayerTick(TickEvent.PlayerTickEvent event) {
        if (event.player instanceof EntityPlayerMP) {
            EntityPlayerMP player = (EntityPlayerMP) event.player;
            enforceGameModeRestriction(player);
        }
    }

    private void enforceGameModeRestriction(EntityPlayerMP player) {
        GameType forcedGameMode = playerForcedGameModes.get(player.getName());
        if (forcedGameMode != null && player.interactionManager.getGameType() != forcedGameMode) {
            player.setGameType(forcedGameMode);
            LangNetworkHandler.sendTranslationMessage(player, "message.forced_mode", Dateiverwaltung.warning, forcedGameMode.getName());
        }
    }
}
