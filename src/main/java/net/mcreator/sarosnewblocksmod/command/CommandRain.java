package net.mcreator.sarosnewblocksmod.command;

import net.minecraftforge.fml.common.event.FMLServerStartingEvent;
import net.minecraft.util.math.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.command.ICommandSender;
import net.minecraft.command.ICommand;
import net.minecraft.util.text.TextComponentTranslation;
import net.mcreator.sarosnewblocksmod.Dateiverwaltung;
import net.mcreator.sarosnewblocksmod.ElementsSarosNewBlocksModMod;
import net.mcreator.sarosnewblocksmod.network.LangNetworkHandler;

import java.util.ArrayList;
import java.util.List;

@ElementsSarosNewBlocksModMod.ModElement.Tag
public class CommandRain extends ElementsSarosNewBlocksModMod.ModElement {
    public CommandRain(ElementsSarosNewBlocksModMod instance) {
        super(instance, 265);
    }

    @Override
    public void serverLoad(FMLServerStartingEvent event) {
        event.registerServerCommand(new CommandHandler());
    }

    public static class CommandHandler implements ICommand {
        private final String standardFarbe = Dateiverwaltung.standard;
        private final String warningFarbe = Dateiverwaltung.warning;

        @Override
        public int compareTo(ICommand c) {
            return getName().compareTo(c.getName());
        }

        @Override
        public boolean checkPermission(MinecraftServer server, ICommandSender sender) {
            return sender.canUseCommand(2, "minewache.command.rain");
        }

        @Override
        public List<String> getAliases() {
            return new ArrayList<>();
        }

        @Override
        public List<String> getTabCompletions(MinecraftServer server, ICommandSender sender, String[] args, BlockPos pos) {
            return new ArrayList<>();
        }

        @Override
        public boolean isUsernameIndex(String[] string, int index) {
            return false;
        }

        @Override
        public String getName() {
            return "rain";
        }

        @Override
        public String getUsage(ICommandSender sender) {
            // Usage message using LangNetworkHandler
            return new TextComponentTranslation("command.rain.usage").getUnformattedText();
        }

        @Override
        public void execute(MinecraftServer server, ICommandSender sender, String[] cmd) {
            if (cmd.length > 0) {
                LangNetworkHandler.sendTranslationMessage(sender, "message.rain.argument_error", Dateiverwaltung.warning);
                return;
            }

            server.getWorld(0).getWorldInfo().setCleanWeatherTime(0);
            server.getWorld(0).getWorldInfo().setRainTime(6000); // 5 minutes of rain
            server.getWorld(0).getWorldInfo().setThunderTime(0);
            server.getWorld(0).getWorldInfo().setRaining(true);
            server.getWorld(0).getWorldInfo().setThundering(false);

            LangNetworkHandler.sendTranslationMessage(sender, "message.rain.success", Dateiverwaltung.standard);
        }
    }
}
