package net.mcreator.sarosnewblocksmod.procedure;

import net.minecraft.world.World;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.math.BlockPos;
import net.minecraft.tileentity.TileEntity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.Entity;
import net.minecraft.block.state.IBlockState;
import net.minecraft.util.EnumFacing;

import net.mcreator.sarosnewblocksmod.ElementsSarosNewBlocksModMod;

import java.util.Map;
import net.minecraft.nbt.NBTTagCompound;

@ElementsSarosNewBlocksModMod.ModElement.Tag
public class ProcedureForSaleSignBlockAdded extends ElementsSarosNewBlocksModMod.ModElement {
    public ProcedureForSaleSignBlockAdded(ElementsSarosNewBlocksModMod instance) {
        super(instance, 316);
    }

    public static void executeProcedure(Map<String, Object> dependencies) {
        if (dependencies.get("entity") == null) {
            System.err.println("Failed to load dependency entity for procedure ForSaleSignBlockAdded!");
            return;
        }
        if (dependencies.get("x") == null) {
            System.err.println("Failed to load dependency x for procedure ForSaleSignBlockAdded!");
            return;
        }
        if (dependencies.get("y") == null) {
            System.err.println("Failed to load dependency y for procedure ForSaleSignBlockAdded!");
            return;
        }
        if (dependencies.get("z") == null) {
            System.err.println("Failed to load dependency z for procedure ForSaleSignBlockAdded!");
            return;
        }
        if (dependencies.get("world") == null) {
            System.err.println("Failed to load dependency world for procedure ForSaleSignBlockAdded!");
            return;
        }
        if (dependencies.get("facing") == null) {
            System.err.println("Failed to load dependency facing for procedure ForSaleSignBlockAdded!");
            return;
        }
        
        Entity entity = (Entity) dependencies.get("entity");
        int x = (int) dependencies.get("x");
        int y = (int) dependencies.get("y");
        int z = (int) dependencies.get("z");
        World world = (World) dependencies.get("world");
        EnumFacing facing = (EnumFacing) dependencies.get("facing");

        if (!world.isRemote) {
            BlockPos _bp = new BlockPos(x, y, z);
            TileEntity _tileEntity = world.getTileEntity(_bp);
            IBlockState _bs = world.getBlockState(_bp);
            if (_tileEntity != null) {
                NBTTagCompound compound = _tileEntity.getTileData();
                // Clear existing data
                compound.removeTag("mx");
                compound.removeTag("my");
                compound.removeTag("mz");
                compound.removeTag("mf");

                // Set new data
                compound.setString("mx", String.valueOf(x));
                compound.setString("my", String.valueOf(y));
                compound.setString("mz", String.valueOf(z));
                compound.setString("mf", facing.getName());
                
                _tileEntity.markDirty(); // Mark the TileEntity as dirty to ensure it gets saved
                world.notifyBlockUpdate(_bp, _bs, _bs, 3);
            }
        }
        //if (entity instanceof EntityPlayer && !entity.world.isRemote) {
        //    ((EntityPlayer) entity).sendStatusMessage(new TextComponentString("Block placed at: " + x + ", " + y + ", " + z + " facing: " + facing), false);
//        }
    }
}
