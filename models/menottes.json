{"credit": "Made By 02h30 | GreenPeople_", "textures": {"0": "blocks/cauldron_inner", "1": "blocks/stone", "particle": "blocks/cauldron_inner"}, "elements": [{"name": "hexadecagon", "from": [7.9875, 27.87351, 16.28112], "to": [8.7, 30.27351, 19.06589], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 27.87351, -3.06589], "to": [8.7, 30.27351, -0.28112], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 27.87351, 16.28112], "to": [7.2625, 30.27351, 19.06589], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 27.87351, -3.06589], "to": [7.2625, 30.27351, -0.28112], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 20.86596, 20.96341], "to": [8.7, 23.26596, 23.74818], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 20.86596, -7.74818], "to": [8.7, 23.26596, -4.96341], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 20.86596, 20.96341], "to": [7.2625, 23.26596, 23.74818], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 20.86596, -7.74818], "to": [7.2625, 23.26596, -4.96341], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 12.6, 22.60761], "to": [8.7, 15, 25.39239], "rotation": {"angle": 0, "axis": "y", "origin": [7.625, 13.8, 24]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 12.6, -9.39239], "to": [8.7, 15, -6.60761], "rotation": {"angle": 0, "axis": "y", "origin": [7.625, 13.8, -8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 13.0415, 22.32555], "to": [8.7, 13.629, 23.17282], "rotation": {"angle": 22.5, "axis": "x", "origin": [7.625, 14.2415, 21.78044]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 13.0415, -7.17282], "to": [8.7, 13.629, -6.32555], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.625, 14.2415, -5.78044]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 12.6, 22.60761], "to": [7.2625, 15, 25.39239], "rotation": {"angle": 0, "axis": "y", "origin": [7.625, 13.8, 24]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 12.6, -9.39239], "to": [7.2625, 15, -6.60761], "rotation": {"angle": 0, "axis": "y", "origin": [7.625, 13.8, -8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 13.0415, 22.32555], "to": [7.2625, 13.629, 23.17282], "rotation": {"angle": 22.5, "axis": "x", "origin": [7.625, 14.2415, 21.78044]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 13.0415, -7.17282], "to": [7.2625, 13.629, -6.32555], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.625, 14.2415, -5.78044]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.08404, 19.96341], "to": [8.7, 6.73404, 23.74818], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.08404, -7.74818], "to": [8.7, 6.73404, -3.96341], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 14.2875, 21.26034], "to": [8.7, 15, 22.60761], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 13.8, 21.21523]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 14.2875, -6.60761], "to": [8.7, 15, -5.26034], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 13.8, -5.21523]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 14.2875, 21.26034], "to": [7.2625, 15, 22.60761], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 13.8, 21.21523]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 14.2875, -6.60761], "to": [7.2625, 15, -5.26034], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 13.8, -5.21523]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 13.62725, 19.47828], "to": [8.7, 14.5585, 21.82555], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 13.3585, 20.43316]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 13.62725, -5.82555], "to": [8.7, 14.5585, -3.47828], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 13.3585, -4.43316]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 13.62725, 19.47828], "to": [7.2625, 14.5585, 21.82555], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 13.3585, 20.43316]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 13.62725, -5.82555], "to": [7.2625, 14.5585, -3.47828], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 13.3585, -4.43316]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 13.44347, 17.64213], "to": [8.7, 14.15597, 19.3644], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 12.95597, 17.97202]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 13.44347, -3.3644], "to": [8.7, 14.15597, -1.64213], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 12.95597, -1.97202]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 13.44347, 17.64213], "to": [7.2625, 14.15597, 19.3644], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 12.95597, 17.97202]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 13.44347, -3.3644], "to": [7.2625, 14.15597, -1.64213], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 12.95597, -1.97202]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 13.25153, 17.51743], "to": [8.05363, 13.92128, 19.13636], "rotation": {"angle": 0, "axis": "x", "origin": [8.2575, 12.79328, 17.82752]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 13.25153, -3.13636], "to": [8.05363, 13.92128, -1.51743], "rotation": {"angle": 0, "axis": "x", "origin": [8.2575, 12.79328, -1.82752]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 12.59155, 16.52679], "to": [8.7, 14.72593, 18.56156], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 13.52593, 16.16918]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 12.59155, -2.56156], "to": [8.7, 14.72593, -0.52679], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 13.52593, -0.16918]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 12.59155, 16.52679], "to": [7.2625, 14.72593, 18.56156], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 13.52593, 16.16918]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 12.59155, -2.56156], "to": [7.2625, 14.72593, -0.52679], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 13.52593, -0.16918]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 12.45072, 16.469], "to": [8.05363, 14.45703, 18.38169], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.2575, 13.32903, 16.13285]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 12.45072, -2.38169], "to": [8.05363, 14.45703, -0.469], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.2575, 13.32903, -0.13285]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 12.36335, 15.67401], "to": [8.7, 14.49773, 16.95879], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 13.29773, 15.5664]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 12.36335, -0.95879], "to": [8.7, 14.49773, 0.32599], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 13.29773, 0.4336]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 12.36335, 15.67401], "to": [7.2625, 14.49773, 16.95879], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 13.29773, 15.5664]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 12.36335, -0.95879], "to": [7.2625, 14.49773, 0.32599], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 13.29773, 0.4336]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 12.23622, 15.6674], "to": [8.05363, 14.24253, 16.87508], "rotation": {"angle": 0, "axis": "x", "origin": [8.2575, 13.11453, 15.56624]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 12.23622, -0.87508], "to": [8.05363, 14.24253, 0.3326], "rotation": {"angle": 0, "axis": "x", "origin": [8.2575, 13.11453, 0.43376]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 11.47841, 15.49207], "to": [8.7, 15.05028, 16.96434], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 12.43607, 16.98617]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 11.47841, -0.96434], "to": [8.7, 15.05028, 0.50793], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 12.43607, -0.98617]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 11.47841, 15.49207], "to": [7.2625, 15.05028, 16.96434], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 12.43607, 16.98617]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 11.47841, -0.96434], "to": [7.2625, 15.05028, 0.50793], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 12.43607, -0.98617]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 11.40437, 15.49637], "to": [8.05363, 14.76193, 16.8803], "rotation": {"angle": -45, "axis": "x", "origin": [8.2575, 12.30457, 16.90082]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 11.40437, -0.8803], "to": [8.05363, 14.76193, 0.50363], "rotation": {"angle": 45, "axis": "x", "origin": [8.2575, 12.30457, -0.90082]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.31626, 15.59363], "to": [8.7, 12.22603, 18.11863], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 12.24786, 16.50472]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.31626, -2.11863], "to": [8.7, 12.22603, 0.40637], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 12.24786, -0.50472]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.06626, 15.59363], "to": [8.7, 8.31626, 17.18113], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 12.24786, 16.50472]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.06626, -1.18113], "to": [8.7, 8.31626, 0.40637], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 12.24786, -0.50472]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.62876, 15.59363], "to": [8.7, 7.87876, 17.18113], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 12.24786, 16.50472]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.62876, -1.18113], "to": [8.7, 7.87876, 0.40637], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 12.24786, -0.50472]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.62876, 17.80613], "to": [8.7, 8.37876, 18.11863], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 12.24786, 16.50472]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.62876, -2.11863], "to": [8.7, 8.37876, -1.80613], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 12.24786, -0.50472]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.4394, 15.69803], "to": [8.7, 7.6894, 16.19803], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 7.5644, 15.94803]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.4394, -0.19803], "to": [8.7, 7.6894, 0.30197], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 7.5644, 0.05197]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.21187, 15.46255], "to": [8.7, 8.46187, 15.96255], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 8.33687, 15.71255]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.21187, 0.03745], "to": [8.7, 8.46187, 0.53745], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 8.33687, 0.28745]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.6269, 15.32303], "to": [8.7, 8.1106, 15.6435], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 7.5644, 15.94803]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.6269, 0.3565], "to": [8.7, 8.1106, 0.67697], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 7.5644, 0.05197]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.70451, 15.92769], "to": [8.7, 8.18821, 16.24817], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 8.25071, 15.77942]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.70451, -0.24817], "to": [8.7, 8.18821, 0.07231], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 8.25071, 0.22058]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 7.62876, 13.89051], "to": [7.2625, 12.22603, 15.61863], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 12.24786, 16.50472]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 7.62876, 0.38137], "to": [7.2625, 12.22603, 2.10949], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 12.24786, -0.50472]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 7.7857, 13.9909], "to": [8.05363, 12.10713, 15.61534], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.2575, 12.12765, 16.44826]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 7.7857, 0.38466], "to": [8.05363, 12.10713, 2.0091], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.2575, 12.12765, -0.44826]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.47514, 13.13573], "to": [8.7, 13.07242, 14.86385], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 10.77378, 13.99979]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 7.62876, 0.38137], "to": [8.7, 12.22603, 2.10949], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 12.24786, -0.50472]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.13351, 12.32185], "to": [8.7, 8.98078, 13.8781], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 9.00261, 14.93607]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.13351, 2.1219], "to": [8.7, 8.98078, 3.67815], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 9.00261, 1.06393]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 8.13351, 12.32185], "to": [7.2625, 8.98078, 13.8781], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 9.00261, 14.93607]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 8.13351, 2.1219], "to": [7.2625, 8.98078, 3.67815], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 9.00261, 1.06393]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 8.26016, 12.51636], "to": [8.05363, 9.0566, 13.97924], "rotation": {"angle": 0, "axis": "x", "origin": [8.2575, 9.07712, 14.97372]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 8.26016, 2.02076], "to": [8.05363, 9.0566, 3.48364], "rotation": {"angle": 0, "axis": "x", "origin": [8.2575, 9.07712, 1.02628]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.28499, 12.1145], "to": [8.7, 9.13226, 13.15513], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 9.15409, 14.72872]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.28499, 2.84487], "to": [8.7, 9.13226, 3.8855], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 9.15409, 1.27128]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 8.28499, 12.1145], "to": [7.2625, 9.13226, 13.15513], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 9.15409, 14.72872]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 8.28499, 2.84487], "to": [7.2625, 9.13226, 3.8855], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 9.15409, 1.27128]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 8.40255, 12.32146], "to": [8.05363, 9.19899, 13.29964], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.2575, 9.21951, 14.77882]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 8.40255, 2.70036], "to": [8.05363, 9.19899, 3.67854], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.2575, 9.21951, 1.22118]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.34559, 11.86497], "to": [8.7, 9.19286, 13.46809], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 9.21469, 14.47918]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 8.34559, 2.53191], "to": [8.7, 9.19286, 4.13503], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 9.21469, 1.52082]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 8.34559, 11.86497], "to": [7.2625, 9.19286, 13.46809], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 9.21469, 14.47918]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 8.34559, 2.53191], "to": [7.2625, 9.19286, 4.13503], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 9.21469, 1.52082]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 8.45952, 12.08689], "to": [8.05363, 9.25596, 13.59383], "rotation": {"angle": -45, "axis": "x", "origin": [8.2575, 9.27647, 14.54425]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 8.45952, 2.40617], "to": [8.05363, 9.25596, 3.91311], "rotation": {"angle": 45, "axis": "x", "origin": [8.2575, 9.27647, 1.45575]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 6.56097, 14.24728], "to": [8.7, 8.27347, 17.59455], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 9.17519, 14.22545]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 6.56097, -1.59455], "to": [8.7, 8.27347, 1.75272], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 9.17519, 1.77455]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 6.56097, 14.24728], "to": [7.2625, 12.52347, 17.59455], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 9.17519, 14.22545]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 6.56097, -1.59455], "to": [7.2625, 12.52347, 1.75272], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 9.17519, 1.77455]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 6.78198, 14.32626], "to": [8.413, 12.38673, 17.4727], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.2575, 9.23934, 14.30575]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [7.5, 7, 8.5, 8], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.19638, 6.78198, -1.4727], "to": [8.413, 12.38673, 1.67374], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.2575, 9.23934, 1.69425]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [7.5, 7, 8.5, 8], "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.26332, 15.33892], "to": [8.7, 5.97582, 16.1862], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 7.87754, 15.3171]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.26332, -0.1862], "to": [8.7, 5.97582, 0.66108], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 7.87754, 0.6829]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.26332, 15.33892], "to": [7.2625, 5.97582, 16.1862], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 7.87754, 15.3171]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.26332, -0.1862], "to": [7.2625, 5.97582, 0.66108], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 7.87754, 0.6829]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 5.56219, 15.35241], "to": [8.05363, 6.23194, 16.14885], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.2575, 8.01955, 15.33189]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 5.56219, -0.14885], "to": [8.05363, 6.23194, 0.64759], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.2575, 8.01955, 0.66811]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 6.79699, 16.5986], "to": [8.7, 7.64426, 17.3111], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 6.77516, 14.69689]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 6.79699, -1.3111], "to": [8.7, 7.64426, -0.5986], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 6.77516, 1.30311]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 6.79699, 16.5986], "to": [7.2625, 7.64426, 17.3111], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 6.77516, 14.69689]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 6.79699, -1.3111], "to": [7.2625, 7.64426, -0.5986], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 6.77516, 1.30311]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 7.00383, 16.53651], "to": [8.05363, 7.80027, 17.20626], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.2575, 6.98331, 14.74889]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 7.00383, -1.20626], "to": [8.05363, 7.80027, -0.53651], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.2575, 6.98331, 1.25111]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 6.57769, 16.23219], "to": [8.7, 8.61246, 17.44469], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 6.55586, 14.83048]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 6.57769, -1.44469], "to": [8.7, 8.61246, -0.23219], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 6.55586, 1.16952]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 6.57769, 16.23219], "to": [7.2625, 8.61246, 17.44469], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 6.55586, 14.83048]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 6.57769, -1.44469], "to": [7.2625, 8.61246, -0.23219], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 6.55586, 1.16952]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 9.34188, 16.8686], "to": [8.7, 11.87665, 17.5811], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 9.32005, 14.96689]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 9.34188, -1.5811], "to": [8.7, 11.87665, -0.8686], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 9.32005, 1.03311]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 9.34188, 16.8686], "to": [7.2625, 11.87665, 17.5811], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 9.32005, 14.96689]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 9.34188, -1.5811], "to": [7.2625, 11.87665, -0.8686], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 9.32005, 1.03311]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 12.30539, 17.1969], "to": [8.7, 15.90267, 18.95628], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 12.28356, 16.34206]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 12.30539, -2.95628], "to": [8.7, 15.90267, -1.1969], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 12.28356, -0.34206]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 12.30539, 17.1969], "to": [7.2625, 15.90267, 18.95628], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 12.28356, 16.34206]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 12.30539, -2.95628], "to": [7.2625, 15.90267, -1.1969], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 12.28356, -0.34206]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 13.99258, 16.0755], "to": [8.7, 14.70508, 16.67277], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 12.09086, 16.6946]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 13.99258, -0.67277], "to": [8.7, 14.70508, -0.0755], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 12.09086, -0.6946]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 13.99258, 16.0755], "to": [7.2625, 14.70508, 16.67277], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 12.09086, 16.6946]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 13.99258, -0.67277], "to": [7.2625, 14.70508, -0.0755], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 12.09086, -0.6946]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 13.76769, 16.04479], "to": [8.05363, 14.43744, 16.60622], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.2575, 11.98008, 16.62674]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.19638, 13.76769, -0.60622], "to": [8.05363, 14.43744, -0.04479], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.2575, 11.98008, -0.62674]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.08404, 19.96341], "to": [7.2625, 6.73404, 23.74818], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.08404, -7.74818], "to": [7.2625, 6.73404, -3.96341], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 1.05435, 18.00603], "to": [7.95, 2.95435, 20.7908], "rotation": {"angle": 45, "axis": "x", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 1.05435, -4.7908], "to": [7.95, 2.95435, -2.00603], "rotation": {"angle": -45, "axis": "x", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 1.60983, 20.28736], "to": [7.95, 3.50983, 23.07213], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 1.60983, -7.07213], "to": [7.95, 3.50983, -4.28736], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 1.25, 22.60761], "to": [7.95, 3.15, 25.39239], "rotation": {"angle": 0, "axis": "y", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 1.25, -9.39239], "to": [7.95, 3.15, -6.60761], "rotation": {"angle": 0, "axis": "y", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 0.02964, 24.61354], "to": [7.95, 1.92964, 27.39832], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 0.02964, -11.39832], "to": [7.95, 1.92964, -8.61354], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, -1.86547, 25.99977], "to": [7.95, 0.03453, 28.78454], "rotation": {"angle": -45, "axis": "x", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, -1.86547, -12.78454], "to": [7.95, 0.03453, -9.99977], "rotation": {"angle": 45, "axis": "x", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 4.54238, 28.63947], "to": [7.95, 6.57715, 30.53947], "rotation": {"angle": 22.5, "axis": "x", "origin": [7.625, 5.93477, 29.58947]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 4.54238, -14.53947], "to": [7.95, 6.57715, -12.63947], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.625, 5.93477, -13.58947]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 5.69517, 28.90896], "to": [7.95, 7.03867, 30.25246], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.625, 7.08755, 29.30246]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 5.69517, -14.25246], "to": [7.95, 7.03867, -12.90896], "rotation": {"angle": 22.5, "axis": "x", "origin": [7.625, 7.08755, -13.30246]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 7.21745, 14.67975], "to": [7.95, 10.87722, 16.57975], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 7.21745, -0.57975], "to": [7.95, 10.87722, 1.32025], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 12.23917, 22.65947], "to": [7.95, 13.58267, 24.00297], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 12.23917, -8.00297], "to": [7.95, 13.58267, -6.65947], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 6.85761, 17], "to": [7.95, 9.64239, 18.9], "rotation": {"angle": 0, "axis": "y", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 6.85761, -2.9], "to": [7.95, 9.64239, -1], "rotation": {"angle": 0, "axis": "y", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 5.63725, 19.00593], "to": [7.95, 8.42202, 20.90593], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 2.59757, 21.93537]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.3, 5.63725, -4.90593], "to": [7.95, 8.42202, -3.00593], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 2.59757, -5.93537]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.71009, 29.91483], "to": [8.7, 6.24487, 31.00233], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 6.42039, 30.08934]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.71009, -15.00233], "to": [8.7, 6.24487, -13.91483], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 6.42039, -14.08934]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.71009, 29.91483], "to": [7.2625, 6.24487, 31.00233], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 6.42039, 30.08934]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.71009, -15.00233], "to": [7.2625, 6.24487, -13.91483], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 6.42039, -14.08934]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.57877, 30.11715], "to": [8.7, 6.11355, 31.20465], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 6.28907, 30.29166]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.57877, -15.20465], "to": [8.7, 6.11355, -14.11715], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 6.28907, -14.29166]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.57877, 30.11715], "to": [7.2625, 6.11355, 31.20465], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 6.28907, 30.29166]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.57877, -15.20465], "to": [7.2625, 6.11355, -14.11715], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 6.28907, -14.29166]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.38003, 30.25382], "to": [8.7, 5.9148, 31.34132], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 6.09032, 30.42833]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.38003, -15.34132], "to": [8.7, 5.9148, -14.25382], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 6.09032, -14.42833]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.38003, 30.25382], "to": [7.2625, 5.9148, 31.34132], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 6.09032, 30.42833]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.38003, -15.34132], "to": [7.2625, 5.9148, -14.25382], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 6.09032, -14.42833]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.14411, 30.30402], "to": [8.7, 5.67888, 31.39152], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 5.8544, 30.47853]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.14411, -15.39152], "to": [8.7, 5.67888, -14.30402], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 5.8544, -14.47853]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.14411, 30.30402], "to": [7.2625, 5.67888, 31.39152], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 5.8544, 30.47853]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.14411, -15.39152], "to": [7.2625, 5.67888, -14.30402], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 5.8544, -14.47853]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 4.70424, 29.72434], "to": [8.7, 5.79174, 30.25911], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 5.61723, 30.43464]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 4.70424, -14.25911], "to": [8.7, 5.79174, -13.72434], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 5.61723, -14.43464]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 4.70424, 29.72434], "to": [7.2625, 5.79174, 30.25911], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 5.61723, 30.43464]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 4.70424, -14.25911], "to": [7.2625, 5.79174, -13.72434], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 5.61723, -14.43464]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 4.50192, 29.59302], "to": [8.7, 5.58942, 30.12779], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 5.41491, 30.30332]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 4.50192, -14.12779], "to": [8.7, 5.58942, -13.59302], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 5.41491, -14.30332]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 4.50192, 29.59302], "to": [7.2625, 5.58942, 30.12779], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 5.41491, 30.30332]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 4.50192, -14.12779], "to": [7.2625, 5.58942, -13.59302], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 5.41491, -14.30332]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 4.33896, 29.10061], "to": [8.7, 5.42646, 29.63538], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 5.25195, 29.8109]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 4.33896, -13.63538], "to": [8.7, 5.42646, -13.10061], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 5.25195, -13.8109]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 4.33896, 29.10061], "to": [7.2625, 5.42646, 29.63538], "rotation": {"angle": 45, "axis": "x", "origin": [8.34375, 5.25195, 29.8109]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 4.33896, -13.63538], "to": [7.2625, 5.42646, -13.10061], "rotation": {"angle": -45, "axis": "x", "origin": [8.34375, 5.25195, -13.8109]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.47137, 28.66074], "to": [8.7, 6.00615, 29.74824], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 5.29585, 29.57373]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.47137, -13.74824], "to": [8.7, 6.00615, -12.66074], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 5.29585, -13.57373]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.47137, 28.66074], "to": [7.2625, 6.00615, 29.74824], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 5.29585, 29.57373]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.47137, -13.74824], "to": [7.2625, 6.00615, -12.66074], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 5.29585, -13.57373]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.60269, 28.45842], "to": [8.7, 6.13747, 29.54592], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 5.42717, 29.37141]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.60269, -13.54592], "to": [8.7, 6.13747, -12.45842], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 5.42717, -13.37141]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.60269, 28.45842], "to": [7.2625, 6.13747, 29.54592], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 5.42717, 29.37141]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.60269, -13.54592], "to": [7.2625, 6.13747, -12.45842], "rotation": {"angle": 0, "axis": "x", "origin": [8.34375, 5.42717, -13.37141]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.80144, 28.32175], "to": [8.7, 6.33621, 29.40925], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 5.62592, 29.23474]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 5.80144, -13.40925], "to": [8.7, 6.33621, -12.32175], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 5.62592, -13.23474]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.80144, 28.32175], "to": [7.2625, 6.33621, 29.40925], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 5.62592, 29.23474]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 5.80144, -13.40925], "to": [7.2625, 6.33621, -12.32175], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 5.62592, -13.23474]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 4.36525, 29.33177], "to": [8.7, 5.45275, 29.92904], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 5.27824, 30.10457]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 4.36525, -13.92904], "to": [8.7, 5.45275, -13.33177], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 5.27824, -14.10457]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 4.36525, 29.33177], "to": [7.2625, 5.45275, 29.92904], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.34375, 5.27824, 30.10457]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 4.36525, -13.92904], "to": [7.2625, 5.45275, -13.33177], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.34375, 5.27824, -14.10457]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 6.04511, 28.6], "to": [8.7, 9.39239, 31], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, 6.04511, -15], "to": [8.7, 9.39239, -12.6], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 6.04511, 28.6], "to": [7.2625, 9.39239, 31], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, 6.04511, -15], "to": [7.2625, 9.39239, -12.6], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, -1.65835, 26.9558], "to": [8.7, 1.12642, 29.3558], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [7.9875, -1.65835, -13.3558], "to": [8.7, 1.12642, -10.9558], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, -1.65835, 26.9558], "to": [7.2625, 1.12642, 29.3558], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 2.4]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#1"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "west": {"uv": [0, 0, 1, 1], "texture": "#1"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.55, -1.65835, -13.3558], "to": [7.2625, 1.12642, -10.9558], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 13.6]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#1"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#1"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#1"}, "west": {"uv": [1, 0, 0, 1], "texture": "#1"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#1"}}}, {"name": "hexadecagon", "from": [6.46, 9.17199, 29.97763], "to": [8.79, 9.97199, 30.13676], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 9.17199, -14.13676], "to": [8.79, 9.97199, -13.97763], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 6.38775, 16.41001], "to": [8.79, 7.18775, 16.56914], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 6.38775, -0.56914], "to": [8.79, 7.18775, -0.41001], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 3.47094, 14.55386], "to": [8.79, 4.27094, 14.71299], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 3.47094, 1.28701], "to": [8.79, 4.27094, 1.44614], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, -0.24137, 19.50361], "to": [8.79, 0.55863, 19.66274], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, -0.24137, -3.66274], "to": [8.79, 0.55863, -3.50361], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 7.30071, 30.23078], "to": [8.79, 8.10071, 30.38991], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 7.30071, -14.38991], "to": [8.79, 8.10071, -14.23078], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 9.92052, 16.63046], "to": [8.79, 10.72052, 16.78959], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 9.92052, -0.78959], "to": [8.79, 10.72052, -0.63046], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 7.93605, 13.79938], "to": [8.79, 8.73605, 13.95851], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 7.93605, 2.04149], "to": [8.79, 8.73605, 2.20062], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 2.61214, 16.95171], "to": [8.79, 3.41214, 17.11084], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 2.61214, -1.11084], "to": [8.79, 3.41214, -0.95171], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 5.475, 29.74856], "to": [8.79, 6.275, 29.90769], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 5.475, -13.90769], "to": [8.79, 6.275, -13.74856], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 13.1, 18.18606], "to": [8.79, 13.9, 18.34519], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 13.1, -2.34519], "to": [8.79, 13.9, -2.18606], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 12.35, 14.81106], "to": [8.79, 13.15, 14.97019], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 12.35, 1.02981], "to": [8.79, 13.15, 1.18894], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 6.225, 15.68606], "to": [8.79, 7.025, 15.84519], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 6.225, 0.15481], "to": [8.79, 7.025, 0.31394], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 3.9728, 28.60438], "to": [8.79, 4.7728, 28.76351], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 3.9728, -12.76351], "to": [8.79, 4.7728, -12.60438], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 15.44216, 20.83998], "to": [8.79, 16.24216, 20.99911], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 15.44216, -4.99911], "to": [8.79, 16.24216, -4.83998], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 16.04081, 17.43487], "to": [8.79, 16.84081, 17.594], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 16.04081, -1.594], "to": [8.79, 16.84081, -1.43487], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 10.0472, 15.89933], "to": [8.79, 10.8472, 16.05846], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 10.0472, -0.05846], "to": [8.79, 10.8472, 0.10067], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 3.02281, 26.97242], "to": [8.79, 3.82281, 27.13155], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 3.02281, -11.13155], "to": [8.79, 3.82281, -10.97242], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 16.59042, 24.18819], "to": [8.79, 17.39042, 24.34732], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 16.59042, -8.34732], "to": [8.79, 17.39042, -8.18819], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 18.44658, 21.27137], "to": [8.79, 19.24658, 21.4305], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 18.44658, -5.4305], "to": [8.79, 19.24658, -5.27137], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 13.49683, 17.55906], "to": [8.79, 14.29683, 17.71819], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 13.49683, -1.71819], "to": [8.79, 14.29683, -1.55906], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 7.62115, 29.91035], "to": [8.79, 7.78028, 30.71035], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 7.62115, -14.71035], "to": [8.79, 7.78028, -13.91035], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 10.24095, 16.31003], "to": [8.79, 10.40008, 17.11003], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 10.24095, -1.11003], "to": [8.79, 10.40008, -0.31003], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 8.25648, 13.47895], "to": [8.79, 8.41561, 14.27895], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 8.25648, 1.72105], "to": [8.79, 8.41561, 2.52105], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 2.93257, 16.63128], "to": [8.79, 3.0917, 17.43128], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 2.93257, -1.43128], "to": [8.79, 3.0917, -0.63128], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 5.79544, 29.42813], "to": [8.79, 5.95456, 30.22813], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 5.79544, -14.22812], "to": [8.79, 5.95456, -13.42813], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 13.42044, 17.86563], "to": [8.79, 13.57956, 18.66562], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 13.42044, -2.66562], "to": [8.79, 13.57956, -1.86563], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 12.67044, 14.49063], "to": [8.79, 12.82956, 15.29062], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 12.67044, 0.70938], "to": [8.79, 12.82956, 1.50937], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 6.54544, 15.36563], "to": [8.79, 6.70456, 16.16562], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 6.54544, -0.16562], "to": [8.79, 6.70456, 0.63437], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 4.29324, 28.28394], "to": [8.79, 4.45237, 29.08394], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 4.29324, -13.08394], "to": [8.79, 4.45237, -12.28394], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 15.76259, 20.51955], "to": [8.79, 15.92172, 21.31955], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 15.76259, -5.31955], "to": [8.79, 15.92172, -4.51955], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 16.36124, 17.11444], "to": [8.79, 16.52037, 17.91444], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 16.36124, -1.91444], "to": [8.79, 16.52037, -1.11444], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 10.36763, 15.5789], "to": [8.79, 10.52676, 16.3789], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 25.48]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.46, 10.36763, -0.3789], "to": [8.79, 10.52676, 0.4211], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, -9.48]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 13.77923, 7.98759], "to": [7.979, 14.23723, 8.40132], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.88948, 11.15225, 10.8971], "to": [9.30322, 11.92275, 11.3551], "rotation": {"angle": -45, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [5.53073, 11.15225, 7.53834], "to": [5.94446, 11.92275, 7.99634], "rotation": {"angle": -45, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 10.09319, 6.53506], "to": [7.979, 10.50693, 6.99306], "rotation": {"angle": 45, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [5.74055, 11.15225, 10.56888], "to": [6.19855, 11.92275, 10.98261], "rotation": {"angle": 22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.55829, 11.15225, 6.18045], "to": [8.01629, 11.92275, 6.59419], "rotation": {"angle": 22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.7892, 6.98884], "to": [7.979, 13.2472, 7.40258], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 13.77923, 7.59868], "to": [7.979, 14.23723, 8.01241], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 9.70754, 0.91288], "to": [7.979, 10.12127, 1.37088], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 5.10886, 7.15012]}, "faces": {"north": {"uv": [1, 0, 0, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "down": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 13.3092, 9.96168], "to": [7.979, 13.7672, 10.37542], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.20008, 11.15225, 11.35774], "to": [8.61382, 11.92275, 11.81574], "rotation": {"angle": -22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.38234, 11.15225, 6.96932], "to": [6.79607, 11.92275, 7.42732], "rotation": {"angle": -22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 10.66094, 6.15571], "to": [7.979, 11.07467, 6.61371], "rotation": {"angle": 22.5, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 13.3092, 5.62458], "to": [7.979, 13.7672, 6.03832], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.1195, 11.60563], "to": [7.979, 12.5775, 12.01937], "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.38688, 11.15225, 11.5195], "to": [7.80062, 11.92275, 11.9775], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.38688, 11.15225, 6.7695], "to": [7.80062, 11.92275, 7.2275], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.33063, 6.0225], "to": [7.979, 11.74437, 6.4805], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.1195, 3.98063], "to": [7.979, 12.5775, 4.39437], "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 10.39124, 12.66916], "to": [7.979, 10.84924, 13.0829], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.57368, 11.15225, 11.35774], "to": [6.98742, 11.92275, 11.81574], "rotation": {"angle": 22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.39143, 11.15225, 6.96932], "to": [8.80516, 11.92275, 7.42732], "rotation": {"angle": 22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.00033, 6.15571], "to": [7.979, 12.41406, 6.61371], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 10.39124, 2.9171], "to": [7.979, 10.84924, 3.33084], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 8.38755, 12.99037], "to": [7.979, 8.84555, 13.4041], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [5.88428, 11.15225, 10.8971], "to": [6.29802, 11.92275, 11.3551], "rotation": {"angle": 45, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.24304, 11.15225, 7.53834], "to": [9.65677, 11.92275, 7.99634], "rotation": {"angle": 45, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.56807, 6.53506], "to": [7.979, 12.98181, 6.99306], "rotation": {"angle": -45, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 8.38755, 2.5959], "to": [7.979, 8.84555, 3.00963], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.15723, 7.98759], "to": [7.979, 12.61523, 8.40132], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.09399, 11.15225, 8.47961], "to": [8.50773, 11.92275, 8.93761], "rotation": {"angle": -45, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [4.73523, 11.15225, 5.12085], "to": [5.14897, 11.92275, 5.57885], "rotation": {"angle": -45, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.41902, 9.48289], "to": [7.979, 11.83276, 9.94089], "rotation": {"angle": 45, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.15723, 7.59868], "to": [7.979, 12.61523, 8.01241], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.6872, 9.96168], "to": [7.979, 12.1452, 10.37542], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.76956, 11.15225, 8.69638], "to": [8.1833, 11.92275, 9.15438], "rotation": {"angle": -22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [5.95182, 11.15225, 4.30795], "to": [6.36556, 11.92275, 4.76595], "rotation": {"angle": -22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.37847, 9.50998], "to": [7.979, 11.7922, 9.96798], "rotation": {"angle": 22.5, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.6872, 5.62458], "to": [7.979, 12.1452, 6.03832], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 10.4975, 11.60563], "to": [7.979, 10.9555, 12.01937], "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.38688, 11.15225, 8.7725], "to": [7.80062, 11.92275, 9.2305], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.38688, 11.15225, 4.0225], "to": [7.80062, 11.92275, 4.4805], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.33063, 9.5195], "to": [7.979, 11.74437, 9.9775], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 10.4975, 3.98063], "to": [7.979, 10.9555, 4.39437], "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 8.76924, 12.66916], "to": [7.979, 9.22724, 13.0829], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.0042, 11.15225, 8.69638], "to": [7.41794, 11.92275, 9.15438], "rotation": {"angle": 22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.82194, 11.15225, 4.30795], "to": [9.23568, 11.92275, 4.76595], "rotation": {"angle": 22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.2828, 9.50998], "to": [7.979, 11.69653, 9.96798], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 8.76924, 2.9171], "to": [7.979, 9.22724, 3.33084], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 6.14055, 12.99037], "to": [7.979, 7.22355, 13.5916], "rotation": {"angle": -45, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.67977, 11.15225, 8.47961], "to": [7.09351, 11.92275, 8.93761], "rotation": {"angle": 45, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [10.03853, 11.15225, 5.12085], "to": [10.45227, 11.92275, 5.57885], "rotation": {"angle": 45, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.24224, 9.48289], "to": [7.979, 11.65598, 9.94089], "rotation": {"angle": -45, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 6.14055, 2.4084], "to": [7.979, 7.22355, 3.00963], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.52034, 9.12855], "to": [7.979, 12.93407, 9.58655], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.98895, 11.15225, 10.56888], "to": [9.44695, 11.92275, 10.98261], "rotation": {"angle": -22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.17121, 11.15225, 6.18045], "to": [7.62921, 11.92275, 6.59419], "rotation": {"angle": -22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 9.8278, 6.98884], "to": [7.979, 10.2858, 7.40258], "rotation": {"angle": 22.5, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.52034, 6.41345], "to": [7.979, 12.93407, 6.87145], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.33063, 10.7725], "to": [7.979, 11.74437, 11.2305], "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.17575, 11.15225, 9.60563], "to": [8.63375, 11.92275, 11.14437], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.17575, 11.15225, 4.85563], "to": [8.63375, 11.92275, 6.39437], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 10.4975, 6.85563], "to": [7.979, 10.9555, 9.14437], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.33063, 4.7695], "to": [7.979, 11.74437, 5.2275], "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 9.60237, 11.83603], "to": [7.979, 10.01611, 12.29403], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.79307, 11.15225, 9.52951], "to": [8.25107, 11.92275, 9.94325], "rotation": {"angle": 22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.61081, 11.15225, 5.14108], "to": [10.06881, 11.92275, 5.55482], "rotation": {"angle": 22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 10.44966, 8.72112], "to": [7.979, 10.90766, 9.13485], "rotation": {"angle": -22.5, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 9.60237, 3.70597], "to": [7.979, 10.01611, 4.16397], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.52034, 10.75055], "to": [7.979, 12.93407, 11.70855], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.93643, 11.15225, 9.52951], "to": [7.39443, 11.92275, 9.94325], "rotation": {"angle": -22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [5.11869, 11.15225, 5.14108], "to": [5.57669, 11.92275, 5.55482], "rotation": {"angle": -22.5, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.16734, 8.72112], "to": [7.979, 12.62534, 9.13485], "rotation": {"angle": 22.5, "axis": "x", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.52034, 4.29145], "to": [7.979, 12.93407, 5.24945], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.14313, 12.3945], "to": [7.979, 11.74437, 13.4775], "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.55375, 11.15225, 9.60563], "to": [7.01175, 11.92275, 11.14437], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [6.55375, 11.15225, 4.85563], "to": [7.01175, 11.92275, 6.39437], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 12.1195, 6.85563], "to": [7.979, 12.5775, 9.14437], "rotation": {"angle": 0, "axis": "y", "origin": [7.59375, 11.5375, 8.8125]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 11.14313, 2.5225], "to": [7.979, 11.74437, 3.6055], "faces": {"north": {"uv": [0, 1, 1, 0], "rotation": 270, "texture": "#0"}, "east": {"uv": [1, 0, 0, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 1, 1, 0], "rotation": 90, "texture": "#0"}, "west": {"uv": [1, 0, 0, 1], "texture": "#0"}, "up": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [1, 0, 0, 1], "rotation": 270, "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.2085, 9.60237, 13.45803], "to": [7.979, 10.01611, 13.91603], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [0, 0, 1, 1], "rotation": 90, "texture": "#0"}, "east": {"uv": [0, 0, 1, 1], "rotation": 180, "texture": "#0"}, "south": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "west": {"uv": [0, 0, 1, 1], "texture": "#0"}, "up": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}, "down": {"uv": [0, 0, 1, 1], "rotation": 270, "texture": "#0"}}}], "display": {"thirdperson_righthand": {"translation": [0, 0, -3.25], "scale": [0.21, 0.21, 0.21]}, "thirdperson_lefthand": {"translation": [0, 0, -3.25], "scale": [0.21, 0.21, 0.21]}, "firstperson_righthand": {"rotation": [-18, 0, 0], "translation": [1, -1, -4.5], "scale": [0.42, 0.42, 0.42]}, "firstperson_lefthand": {"rotation": [-18, 0, 0], "translation": [1, -1, -4.5], "scale": [0.42, 0.42, 0.42]}, "ground": {"rotation": [0, 0, 90], "translation": [0, -3.25, 0], "scale": [0.27, 0.27, 0.27]}, "gui": {"rotation": [26, -53, 53], "translation": [0.25, 0, 0], "scale": [0.42, 0.42, 0.42]}}, "groups": [{"name": "hexadecagon", "origin": [8, 8, 8], "children": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201]}, {"name": "hexadecagon", "origin": [8, 8, 8], "children": [202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265]}, {"name": "hexadecagon", "origin": [8, 8, 8], "children": [266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345]}]}